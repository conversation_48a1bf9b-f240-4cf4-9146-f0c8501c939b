VITE_APP_BASE_API_PROXY = ""
VITE_APP_CENTER_URL = "https://192.168.2.213:6060"
VITE_APP_LOGIN_URL = "/#/login"
VITE_APP_TITLE = "电车中控控制平台"
# 协议版本号
VITE_APP_VER = "1.0"
# 程序唯一标识符
VITE_APP_APPID = "10a82a88abb75856617ae3c449e6a698"
# 程序密钥
VITE_APP_APPSECRET = "a674e596a59fda9ca58fcbfd711a88dc"
VITE_ROUTER_HISTORY = "hash"
VITE_PUBLIC_PATH = ./
# 站台实时视频接口地址
VITE_APP_STATIONVIDEO_API = REPLACE_VITE_APP_STATIONVIDEO_API
# 站台实时视频测试设备号
VITE_APP_STATIONVIDEO_TESTDEVICE = REPLACE_VITE_APP_STATIONVIDEO_TESTDEVICE

#移动端签名页固定机构名称
VITE_APP_MOBILESIGN_CORPORATIONNAME = REPLACE_VITE_APP_MOBILESIGN_CORPORATIONNAME
#移动端签名页固定机构ID
VITE_APP_MOBILESIGN_CORPORATIONID = REPLACE_VITE_APP_MOBILESIGN_CORPORATIONID
#移动端签名页有效时间,超过本时间,签名页面不可见
VITE_APP_MOBILESIGN_VALIDITYDATE = REPLACE_VITE_APP_MOBILESIGN_VALIDITYDATE
