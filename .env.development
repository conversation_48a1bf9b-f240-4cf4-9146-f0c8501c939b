# 规范要求: "参考http://www.oetlink.com/关于《软件程序运行配置文件》管理规范技术要求.pdf"
# 重点说明: "版本一旦发布之后, 配置文件处于受控状态, 不可轻易变更, 如需变更请走变更流程"

ENV = development

# 项目名称
VITE_APP_TITLE = "电车中控控制平台"

# 平台本地运行端口号
# VITE_PORT = 40087
VITE_PORT = 8052

# 开发环境读取配置文件路径
VITE_PUBLIC_PATH = ./

#高德地图的key
VITE_APP_AMAP_KEY = "41c8c8ef513d4cacfd92bafa96dfe668"

#地图初始中心点位置，格式："经度,纬度"，例："121.42079,28.655716"，默认为""
VITE_APP_AMAP_CENTER_POSITION = ""

#地图初始缩放大小，默认为13
VITE_APP_AMAP_CENTER_ZOOM = 13

# 开发环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "hash"

# API接口地址
VITE_APP_BASE_API_PROXY = "http://47.97.220.200:8004"
# VITE_APP_BASE_API_PROXY = "http://192.168.2.213:40087"
# VITE_APP_BASE_API_PROXY = "https://scs.oetlink.com:38088"
# 主数据页
VITE_APP_CENTER_URL = "https://192.168.2.222:6060"
# 主数据登录页
VITE_APP_LOGIN_URL = "https://192.168.2.222:6060/login"
# 协议版本号
VITE_APP_VER = "1.0"
# 程序唯一标识符
VITE_APP_APPID = "10a82a88abb75856617ae3c449e6a698"
# 程序密钥
VITE_APP_APPSECRET = "a674e596a59fda9ca58fcbfd711a88dc"

# 站台实时视频接口地址
VITE_APP_STATIONVIDEO_API = "https://192.168.2.222:8443/"
# 站台实时视频测试设备号
VITE_APP_STATIONVIDEO_TESTDEVICE = "34020000001320000001"

#移动端签名页固定机构名称
VITE_APP_MOBILESIGN_CORPORATIONNAME = "平湖公交"
#移动端签名页固定机构ID
VITE_APP_MOBILESIGN_CORPORATIONID = "1505961073969202177"
#移动端签名页有效时间,超过本时间,签名页面不可见
VITE_APP_MOBILESIGN_VALIDITYDATE = 1726873375
