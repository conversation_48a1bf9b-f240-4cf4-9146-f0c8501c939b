import { http } from "@/utils/http";

export type corporationListResult = {
  Code: string;
  Data: {
    Items: corporationListItems[];
    TotalCount: number; // 总条数
  };
};

export type corporationListItems = {
  Id: number; // 机构 Id
  IdStr: string; // 机构 IdStr
  Name: string; // 机构名
  Leader: string; // 联系人
  Phone: string; // 联系方式
  CountryId: number; // 国家 Id
  ProvinceId: number; // 省份Id
  CityId: number; // 城市Id
  AddedBy: number; // 创建人 Id
  IsAdmin: boolean; // 是否管理员
  ParentId: number; // 上级机构 Id
  ParentName?: string; //前端自定义参数(上级机构名称)
  CreatedAt: number; // 创建时间, unix timestamp
  UpdatedAt: number; // 修改时间, unix timestamp

  // v2.0
  Country: string; // 国家名
  Province: string; // 省份名
  City: string; // 城市名
  SonNum: number; // 子节点数量
  LineIds: [number]; // 关联线路 Ids
  LineNames: [string]; // 关联线路 names

  // v2.1
  IssVendor: number; // 调度厂家, 0: 没有调度, 1: 律橙调度, 2: 第三方调度
  IssProtocol: number; // 调度协议（与IssVendor,2: 第三方调度配合）,
  // 0: 未知, 1: 天迈, 2: 蓝泰源,
  // 3: 海信, 4: 海康威视, 5: 蓝斯，6；图软
  IssTable: string; // 数据表前缀，如shaoxing, daishan, yuhuan
  IssHost: string; // 第三方调度主机ip端口
  Type: number; // 类型, 1:集团、2:公司、3:分公司、4:部门、5:车队
  Virtual: string; // 编号（蓝凌机构编号)
  Sort: number; // 排序号
};

export type corporationListReq = {
  AuthId?: string; // 用户唯一标识
  ParentId?: number; // 机构 Id
  Name?: string; // 机构名
  Offset?: number; // 从第几行开始查询
  Limit?: number; // 查询数据的个数
  Order?: string; // 查询结果的排序方式, desc(降序)/asc(升序)
};

export type parkingListResult = {
  Code: string;
  Data: {
    Items: parkingListItems[];
    TotalCount: number; // 总条数
  };
};
export type parkingListItems = {
  Id: number; // 场站 Id
  Code: string; // 场站编号
  Name: string; // 场站名
  CorporationId: number; // 机构 Id
  Corporation: string; // 机构名
  ParkingGroupId: number; // 场站分组 Id
  ParkingGroup: string; // 场站分组名
  Lines: [string]; // 停靠线路名
  ParkingType: number; // 场站类型, 1:停车场;2:加油站;3:维修场;4:包车点
  Longitude: [number]; // 场站经度, 原始坐标
  Latitude: [number]; // 场站纬度, 原始坐标
  ShapeType: number; // 图形类型, 0:Multi, 1:Circle, 2:Rectange
  Capacity: number; // 泊位数
  Area: number; // 占地面积
  ChargingBolt: number; // 充电栓数量
  ChargingPlace: number; // 充电位数量
  ParkingQulity: number; // 场站性质, 1-租用；2-划拨；3-占道

  Spots: {
    // 圆形中心点，1:Circle(圆形)
    Longitude: number; // 圆心经度, GPS原始坐标
    Latitude: number; // 圆心纬度, GPS原始坐标
  };
  Radius: number; // 图形类型1:Circle(圆形)的半径，单位：米

  Center: {
    Longitude: number; // 中心点经度, GPS原始坐标
    Latitude: number; // 中心点纬度, GPS原始坐标
  };
  CityCode: string; // 城市代码,国家城市区域代码330101
  District: string; // 区域
  ParkingPorts: [
    // 进出场口列表
    {
      PortType: number; // 场口类型，1:出场口，2:进场口
      PortNum: number; // 进场口或者出场口的编号, 从1开始按序编号
      Radius: number; // 检测半径，单位：米
      Longitude: number; // 经度, GPS原始坐标
      Latitude: number; // 纬度, GPS原始坐标
    }
  ];
  LineParkings: [
    {
      LineName: string; // 线路名称
      LineId: Number; // 线路ID
      DayAndNight: number; // 1-日间停车；2-夜间停车（废弃）
      CorporationId: number; // 线路ID归宿子机构ID
    }
  ];
  UpdatedAt: number; // 更新时间
};

export type parkingListReq = {
  AuthId?: string;
  Name?: string; // 场站名
  Code?: string; // 场站编号
  CorporationId?: number; // 机构 Id
  ParkingGroupId?: number; // 场站分组 Id
  ParkingType?: number; // 场站类型,0:全部, 1:停车场;2:加油站;3:维修场;4:包车点
  Offset?: number; // 从第几行开始查询
  Limit?: number; // 查询数据的个数
  Order?: string; // 查询结果的排序方式, desc(降序)/asc(升序)
};

/* 左侧树类型
// 1:机构;2:机构->线路;3:机构->线路->车辆、机构->车辆;
// 4:机构->司机、机构->线路->司机;5:机构->角色;
// 6:机构->用户,7:机构->线路->车辆->通道（默认显示通道下主码流\子码流）；
// 8:机构->场站/场馆; 9:机构->场站/场馆->通道 ;
// 10:机构->场站/场馆->通道->出入口;
// 11:机构->线路->行车计划、机构->行车计划;
// 12:机构->站点
*/
export enum TreeType {
  Unknown = 0,
  Corporation = 1,
  Line = 2,
  Vehicle = 3,
  Driver = 4,
  Role = 5,
  User = 6,
  Stream = 7,
  Venue = 8,
  Channel = 9,
  Door = 10,
  LinePlan = 11,
  Station = 12,
}

/*左侧树返回参数中 机构类型枚举
// 1:集团
// 2:公司
// 3:分公司
// 4:部门
// 5:车队
*/
export enum CorpType {
  Group       = 1,
  Company     = 2,
  Branch      = 3,
  Department  = 4,
  Fleet       = 5,
}

/* 左侧树返回参数中 Item的解析实体名
*/
export enum TreeItem {
  Corporation = "Corporations",
      Role = "Roles",
      User = "Users",
      /*Line = "Lines",
        ├── LinePlan = "LinePlans",
        ├── Driver = "Drivers",
        ├── Vehicle = "Vehicles",
            ├── Stream = "Channels", */
      Line = "Lines",
      Driver = "Drivers",
      Vehicle = "Vehicles",
          Stream = "Channels", //type=7,机构->线路->车辆->通道(码流)
      Venue = "Venues",
          Channel = "Channels", //type=9,机构->场站/场馆->通道(过道)
              Door = "Doors",
      LinePlan = "LinePlans",
      Station = "Stations",
}

/** 获取机构列表 */
export const getCorporationList = (data?: corporationListReq) => {
  return http.request<corporationListResult>("post", "/app/corporation/list", {
    data
  });
};

/** 获取场站列表 */
export const getParkingList = (data?: parkingListReq) => {
  return http.request<parkingListResult>("post", "/app/parking/list", {
    data
  });
};

/** 树形 */
export const getTreeList = (data?: object) => {};
