import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import { storageLocal, storageSession } from "@pureadmin/utils";
import { router } from "@/router";
import { noCheckAuthIdList } from "@/router/index";
import { roleTypes } from "element-plus";

// const router = useRouter();

export interface DataInfo {
  /** AuthId */
  AuthId: string;
  /** `AuthId`有效时间*/
  ExpireTime: number;
  /** 用户名 */
  Nickname?: string;
  /* 当前authId更新时间 */
  loginTime?: number;
  /** 当前登陆用户的角色 */
  Roles?: Array<number>;
  /** 当前登陆用户的权限 */
  ApiPermissions?: Array<string>;
  /** 用于标识当前账号是否为管理员, 管理员账号显示全部权限*/
  IsAdmin?: boolean;
  /** 账号 */
  UserName?: string;
  CorporationId?: string;
  role?: string;
}

export const sessionKey = "user-info";
const cookieKey_Token = "AuthId";
const cookieKey_expireTime = "ipoc_expireTime";
const cookieKey_loginTime = "ipoc_loginTime";

/** 获取`token` */
export function getToken() {
  return Cookies.get(cookieKey_Token);
}

// 保存authId
export function setToken(authId) {
  setCookie(cookieKey_Token, authId);
}

// 保存登录时间
export function setLoginTime(
  loginTime: number | string,
  expireTime: number | string
) {
  if (expireTime) {
    setCookie(cookieKey_expireTime, expireTime.toString());
  }
  if (loginTime) {
    setCookie(cookieKey_loginTime, loginTime.toString());
  }
}

/**
 * @description 设置用户信息
 */
export function setUserInfo(data: any) {
  // 处理登录响应数据，提取用户信息
  let userData;

  // 如果data包含user字段（登录响应），提取user信息
  if (data.user) {
    userData = {
      ...data.user,
      UserName: data.user.UserName || data.user.username || "",
      Nickname: data.user.Nickname || data.user.nickname || "",
      role: data.user.role || "",
      CorporationId: data.user.CorporationId || "",
      ApiPermissions: data.user.ApiPermissions || [],
      AuthId: data.token || data.AuthId || "",
      ExpireTime: data.ExpireTime || 0
    };
  } else {
    // 直接是用户信息数据
    userData = {
      ...data,
      UserName: data.UserName || data.username || "",
      Nickname: data.Nickname || data.nickname || "",
      role: data.role || "",
      CorporationId: data.CorporationId || "",
      ApiPermissions: data.ApiPermissions || [],
      id: data.id ? data.id.toString() : "0",
      created_at: data.created_at || "",
      updated_at: data.updated_at || ""
    };
  }

  console.log('setUserInfo 保存的用户数据:', userData); // 调试日志

  storageSession().setItem(sessionKey, userData);

  // 同步更新 Pinia store
  const userStore = useUserStoreHook();
  userStore.SET_NICKNAME(userData.Nickname);
  userStore.SET_USERNAME(userData.UserName);
  userStore.SET_ROLE(userData.role);
  userStore.SET_CORPORATIONID(userData.CorporationId);
  if (userData.ApiPermissions) {
    userStore.SET_ROLES(userData.ApiPermissions);
  }
}
// export function setUserInfo(data) {
//   function setSessionKey(
//     Nickname: string,
//     ApiPermissions: Array<string>,
//     IsAdmin: boolean,
//     ExpireTime: number,
//     UserName: string,
//     CorporationId: string,
//     role: string
//   ) {
//     useUserStoreHook().SET_NICKNAME(Nickname);
//     useUserStoreHook().SET_ROLES(ApiPermissions);
//     useUserStoreHook().SET_USERNAME(UserName);
//     useUserStoreHook().SET_CORPORATIONID(CorporationId);
//     useUserStoreHook().SET_ROLE(role);
//     storageSession().setItem(sessionKey, {
//       ExpireTime,
//       Nickname,
//       ApiPermissions,
//       IsAdmin,
//       UserName
//     });
//   }

//   const { Nickname, ApiPermissions, IsAdmin, ExpireTime, UserName } = data;

//   const CorporationId = data.CorporationId.toString();

//   setSessionKey(
//     Nickname,
//     ApiPermissions,
//     IsAdmin,
//     ExpireTime,
//     UserName,
//     CorporationId
//   );
// }

/** 删除`token`以及key值为`user-info`的session信息 */
export function removeToken() {
  Cookies.remove(cookieKey_Token);
  sessionStorage.clear();
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return token;
};

/** cookie存储 */
export function setCookie(name: string, value: string) {
  Cookies.set(name, value, { sameSite: "Lax" });
}
export function getCookie(name: string) {
  return Cookies.get(name);
}

export function removeCookie(name: string) {
  Cookies.remove(name);
}

export let timer = null;
// 开启轮询，每10分钟检测一次authid是否过期
// export const startAuthIdCheckTimerTask = () => {
//   checkAuthId();
//   if (!timer) {
//     timer = setInterval(() => {
//       checkAuthId();
//     }, 10 * 60 * 1000);
//   }
// };

//authId检测并更新
// export const checkAuthId = () => {
//   let path = router.currentRoute.value.fullPath;
//   if (noCheckAuthIdList.includes(path)) {
//     // 部分页面不用检测authId
//     return;
//   }

//   return new Promise(function (_resolve, _reject) {
//     const loginTime = Number(getCookie(cookieKey_loginTime));
//     const expireTime = Number(getCookie(cookieKey_expireTime));
//     const nowDate = parseInt(new Date().getTime() / 1000);
//     const token = getToken();
//     if (token) {
//       if (expireTime - (nowDate - loginTime) <= 1800) {
//         console.log("authId即将过期");
//         useUserStoreHook()
//           .handRefreshToken({ AuthId: getToken() })
//           .then(res => {
//             console.log("authId续期成功");
//             setToken(res?.Data?.AuthId);
//             setLoginTime(nowDate, res?.Data?.ExpireTime);
//           })
//           .catch(() => {
//             clearInterval(timer);
//             console.log("authId续期失败");
//           });
//       } else {
//         console.log("authId未过期");
//       }
//     } else {
//       console.log("authId不存在");
//       jumpToLogin({ sso: true });
//     }
//   });
// };

/** 获取地址栏中的authId值*/
export function getUrlAuthId() {
  const searchParams = new URLSearchParams(location.search);
  return searchParams.get("authId") || searchParams.get("key");
}

/** 跳转主登录页*/
export function jumpToLogin({ sso }) {
  removeToken();
  storageLocal().clear();
  storageSession().clear();

  if (import.meta.env.PROD) {
    let href = import.meta.env.VITE_APP_LOGIN_URL;

    if (sso) {
      // 【单点登录】携带单点登录参数跳转至主数据登录页
      // 登录成功后会直接返回本页面
      href += `?from=${location.origin + location.pathname}&page=${location.hash.split("#")[1] || ""
        }&${location.search.split("?")[1] || ""}`;
    }

    window.location.href = href;
  } else {
    router.push(`/login`);
  }
}

/** 跳回融合云平台*/
export function jumpToCenter() {
  if (import.meta.env.PROD) {
    window.location.href = import.meta.env.VITE_APP_CENTER_URL;
  } else {
    router.push(`/index`);
  }
}
