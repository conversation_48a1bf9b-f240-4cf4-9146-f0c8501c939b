import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  CustomParamsSerializer
} from "axios";
import {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, formatToken, jumpToLogin, getUrlAuthId, removeToken } from "@/utils/auth";
import { Encode2PacketFmt, GetApiBaseUrlPrefix } from "@/utils/http/util";
import { ElMessage, ElMessageBox } from "element-plus";
import JSONbig from "json-bigint";
import { router } from "@/router";
import { noCheckAuthIdList } from "@/router/index";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 60000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
    // Authorization: `Bearer ${getToken()}`
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  },
  baseURL: ""
};
let SessionExpirationMsgAlert = null; //接受提示框

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** token过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新token */
  private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["X-Token"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // 开启进度条动画
        NProgress.start();

        //add by hades, 切换请求地址前缀及封装协议格式
        config.baseURL = GetApiBaseUrlPrefix();
        // if (!config.data?.AuthId) {
        //   config.data.AuthId = getToken();
        // }

        config.data = Encode2PacketFmt(config.data);
        console.log("config1",config);
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          console.log("config2",config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          console.log("config3",config);
          return config;
        }
        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */
        const whiteList = [
          "/app/user/login",
          "/ipoc/thirdparty/UploadFile",
          "/ipoc/thirdparty/SyncStaffSignature",
          "/ipoc/thirdparty/SearchStaff",
          // "api/devices"
        ];
        // 如果是白名单接口，直接返回配置
        if (whiteList.some(v => config.url.indexOf(v) > -1)) {
          console.log("config4",config);
          return config;
        }
        // return whiteList.some(v => config.url.indexOf(v) > -1)
        //   ? config
        //   : new Promise(resolve => {
        //     const data = getToken() || getUrlAuthId();
        //     if (data) {
        //       config.headers["X-Token"] = formatToken(data);
        //       resolve(config);
        //     } else {
        //       let path = router.currentRoute.value.fullPath;
        //       if (noCheckAuthIdList.includes(path)) {
        //         // 部分页面不用检测authId
        //         return;
        //       } else {
        //         //会话过期返回登录页面
        //         console.log(1, config.url);
        //         jumpToLogin({ sso: true });
        //       }
        //     }
        //   });
        // 非白名单接口需要添加 token
        const token = getToken() || getUrlAuthId();
        console.log("token1111", token);
        if (token) {
          // 动态设置 token
          config.headers["X-Token"] = formatToken(token);
          config.headers["Authorization"] = `Bearer ${token}`;
          console.log("config5",config);
          return config;
        } else {
          // 没有 token 的情况
          let path = router.currentRoute.value.fullPath;
          if (noCheckAuthIdList.includes(path)) {
            // 部分页面不用检测authId，直接返回配置
            return config;
          } else {
            // 会话过期返回登录页面
            console.log("没有token，跳转登录页面", config.url);
            jumpToLogin({ sso: true });
            return Promise.reject(new Error("未登录"));
          }
        }
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        const res = response.data;
        const { code, msg } = res;

        // 关闭进度条动画
        NProgress.done();

        // 如果响应码不是 200，则视为错误
        if (code !== 200) {
          // Token 过期情况：LG1002 或 401
          if (code === "LG1002" || code === 401) {
            handleTokenExpired(msg || "登录已过期，请重新登录");
            return Promise.reject(new Error(msg || "登录已过期"));
          } else {
            ElMessage.closeAll("error");
            ElMessage.error({
              message: msg || "Error",
              duration: 5 * 1000
            });
            return Promise.reject(new Error(msg || "Error"));
          }
        }

        // 正常响应处理
        const $config = response.config;
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        return response.data;
      },
      (error: PureHttpError) => {
        // 关闭进度条动画
        NProgress.done();

        // 处理 HTTP 错误
        if (error.response) {
          const { status, data } = error.response;

          // 处理 401 未授权错误（token 过期）
          if (status === 401) {
            handleTokenExpired(data?.msg || "登录已过期，请重新登录");
            return Promise.reject(error);
          }

          // 其他错误
          ElMessage.error({
            message: data?.msg || `请求错误 (${status})`,
            duration: 5 * 1000
          });
        } else {
          // 网络错误等
          ElMessage.error({
            message: error.message || "网络错误",
            duration: 5 * 1000
          });
        }

        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        return Promise.reject($error);
      }
    );
  }


  // private httpInterceptorsResponse(): void {
  //   const instance = PureHttp.axiosInstance;
  //   instance.interceptors.response.use(
  //     (response: PureHttpResponse) => {
  //       //
  //       const res = response.data;
  //       // console.log(res);
  //       const { code, msg } = res;
  //       // if the custom code is not "0", it is judged as an error.
  //       if (code !== 200) {
  //         // LG1002: Token expired;
  //         if (code === "LG1002") {
  //           if (!SessionExpirationMsgAlert) {
  //             SessionExpirationMsgAlert = ElMessageBox.alert(
  //               msg || "Error",
  //               "提示",
  //               {
  //                 confirmButtonText: "确定",
  //                 callback: () => {
  //                   SessionExpirationMsgAlert = null;
  //                   //此为会话过期
  //                   //会话过期返回登录页面
  //                   jumpToLogin({ sso: true });
  //                 }
  //               }
  //             );
  //           }
  //         } else {
  //           ElMessage.closeAll("error");
  //           ElMessage.error({
  //             message: msg || "Error",
  //             duration: 5 * 1000
  //           });
  //           return Promise.reject(new Error(msg || "Error"));
  //         }
  //       }

  //       const $config = response.config;
  //       // 关闭进度条动画
  //       NProgress.done();
  //       // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
  //       if (typeof $config.beforeResponseCallback === "function") {
  //         $config.beforeResponseCallback(response);
  //         return response.data;
  //       }
  //       if (PureHttp.initConfig.beforeResponseCallback) {
  //         PureHttp.initConfig.beforeResponseCallback(response);
  //         return response.data;
  //       }
  //       return response.data;
  //     },
  //     (error: PureHttpError) => {
  //       const $error = error;
  //       $error.isCancelRequest = Axios.isCancel($error);
  //       // 关闭进度条动画
  //       NProgress.done();
  //       // 所有的响应异常 区分来源为取消请求/非取消请求
  //       return Promise.reject($error);
  //     }
  //   );
  // }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig,
      transformResponse: [
        function (data) {
          // 对返回的大整形进行特殊处理
          return JSONbig.parse(data);
        }
      ],
      transformRequest: [
        function (data) {
          // 对发送的大整形进行特殊处理
          return JSONbig.stringify(data);
        }
      ]
    } as PureHttpRequestConfig;

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的post工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("post", url, params, config);
  }

  /** 单独抽离的get工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>("get", url, params, config);
  }
}

export const http = new PureHttp();

// 防止重复弹窗
let isTokenExpiredAlertShown = false;

// 处理 token 过期
function handleTokenExpired(message: string): void {
  if (isTokenExpiredAlertShown) return;

  isTokenExpiredAlertShown = true;

  ElMessageBox.alert(
    message,
    "会话过期提示",
    {
      confirmButtonText: "确定",
      type: "warning",
      showClose: false,        // 隐藏右上角关闭按钮
      closeOnClickModal: false, // 禁止点击遮罩层关闭
      closeOnPressEscape: false, // 禁止按ESC键关闭
      showCancelButton: false,  // 不显示取消按钮
      callback: () => {
        isTokenExpiredAlertShown = false;

        // 清除用户信息和 token
        removeToken();

        // 跳转到登录页
        jumpToLogin({ sso: true });
      }
    }
  );
}

// 去除params包裹的简单HTTP类
class SimpleHttp {
  private axiosInstance: AxiosInstance;

  constructor() {
    this.axiosInstance = Axios.create({
      timeout: 60000,
      headers: {
        Accept: "application/json, text/plain, */*",
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
        // Authorization: `Bearer ${getToken()}`
      },
      baseURL: import.meta.env.VITE_APP_BASE_API_PROXY
    });
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        // 开启进度条动画
        NProgress.start();
        const token = getToken();
        if (token) {
          config.headers["Authorization"] = `Bearer ${token}`;
           config.headers["X-Token"] = formatToken(token);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        // 关闭进度条动画
        NProgress.done();
        // 检查响应状态
        const { code, msg } = response.data || {};

        if (code === 401 || code === "LG1002") {
          handleTokenExpired(msg || "登录已过期，请重新登录");
          return Promise.reject(new Error(msg || "登录已过期"));
        }
        return response.data;
      },
      (error) => {
        // 关闭进度条动画
        NProgress.done();
        // 处理 HTTP 错误
        if (error.response) {
          const { status } = error.response;

          if (status === 401) {
            handleTokenExpired("登录已过期，请重新登录");
            return Promise.reject(error);
          }
        }
        return Promise.reject(error);
      }
    );
  }

  public post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.post(url, data, config);
  }
}

// 导出实例
export const simpleHttp = new SimpleHttp();

