import { defineStore } from "pinia";
import { store } from "@/store";
import { type corporationListItems, getTreeList } from "@/api/center";

interface CorporationType {
  list: corporationListItems[];
}

export const corporationStore = defineStore({
  id: "corporation",
  state: (): CorporationType => ({
    //机构列表
    list: []
  }),
  actions: {
    SET_LIST(list: corporationListItems[]) {
      if (list.length) {
        this.list.push(...list);
      }
    },

    async getCorporationList() {
      // return new Promise<any>((resolve, reject) => {
      //   getTreeList({
      //     TreeType: 1,
      //     Offset: 0,
      //     Limit: 99999999,
      //     Order: "asc",
      //     IsLineAttribute: 0
      //   })
      //     .then(res => {
      //       const data = res?.Data?.Items;
      //       data?.forEach((item: any) => {
      //         item.IdStr = item.Id.toString();
      //         item.ParentIdStr = item.ParentId.toString();
      //       });
      //       useCorporationStoreHook().SET_LIST(data);
      //
      //       resolve(res);
      //     })
      //     .then(err => {
      //       reject(err);
      //     });
      // });
    }
  }
});
export function useCorporationStoreHook() {
  return corporationStore(store);
}
