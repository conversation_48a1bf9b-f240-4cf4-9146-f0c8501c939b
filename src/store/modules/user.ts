import { defineStore } from "pinia";
import { store } from "@/store";
import { userType } from "./types";
import { routerArrays } from "@/layout/types";
import { router, resetRouter } from "@/router";
import { storageSession, subBefore, getQueryMap } from "@pureadmin/utils";
import { getLogin, refreshTokenApi, userDetail, AuthLogin } from "@/api/user";
import { UserResult, RefreshTokenResult } from "@/api/user";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  type DataInfo,
  setLoginTime,
  setToken,
  setUserInfo,
  removeToken,
  sessionKey,
  getCookie
} from "@/utils/auth";

export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    // 用户名
    Nickname: storageSession().getItem<DataInfo>(sessionKey)?.Nickname ?? "",
    // 页面级别权限
    ApiPermissions: [], // 移除sessionStorage依赖，直接初始化空数组
    // 账号
    UserName: storageSession().getItem<DataInfo>(sessionKey)?.UserName ?? "",

    CorporationId:
      storageSession().getItem<DataInfo>(sessionKey)?.CorporationId ?? "",
    role: storageSession().getItem<DataInfo>(sessionKey)?.role ?? ""
  }),
  getters: {
    // 添加getter方便获取用户信息
    userInfo: (state) => ({
      // nickname: state.Nickname,
      // username: state.UserName,
      role: state.role,
      // corporationId: state.CorporationId,
      // apiPermissions: state.ApiPermissions
    }),
    // 判断是否为root用户
    isRoot: (state) => state.role === 'root',
    // 判断是否为admin用户
    isAdmin: (state) => state.role === 'admin'
  },
  actions: {
    /** 存储用户名 */
    SET_NICKNAME(nickname: string) {
      this.Nickname = nickname;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.ApiPermissions = roles;
    },
    /** 存储角色 */
    SET_CORPORATIONID(corporationId: string) {
      this.CorporationId = corporationId;
    },
    /** 存储用户角色 */
    SET_ROLE(role: string) {
      this.role = role;
    },
    /** 存储账号 */
    SET_USERNAME(username: string) {
      this.UserName = username;
    },
    /** 登入 */
    async loginByUsername(data: { username: string; password: string }) {
      return new Promise<UserResult>((resolve, reject) => {
        AuthLogin(data)
          .then(res => {
            const data = res?.data;
            // const expires = data.ExpireTime || 0;
            const { token, user } = data;
            setToken(token);
            this.SET_ROLE(user.role || "");

            // setLoginTime(loginTime, expires);
            setUserInfo(data);
            router.push({ path: "/dss/staff" });
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.UserName = "";
      this.ApiPermissions = [];
      this.Role = "";
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      if (import.meta.env.PROD) {
        const href = import.meta.env.VITE_APP_LOGIN_URL;
        location.replace(href);
      } else {
        router.push("/login");
      }
    },
    /** 刷新`token` */
    async handRefreshToken(data: string) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data.Code === "0") {
              setToken(data.Data.AuthId);
              setLoginTime(parseInt(new Date().getTime() / 1000), "");
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 获取用户详情 */
    async getUserDetail(authId: string) {
      return new Promise((resolve, reject) => {
        userDetail({
          AuthId: authId,
          Id: 0
        })
          .then(data => {
            if (data.Code === "0") {
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    /**单点登录  */
    async ssoLogin() {
      return new Promise((resolve, _reject) => {
        const searchParams = new URLSearchParams(location.search);
        const urlAuthId = searchParams.get("authId") || searchParams.get("key");
        const cookieAuthId = getCookie("ipoc_authId");
        const loginTime =
          searchParams.get("loginTime") || getCookie("ipoc_loginTime");
        const expireTime =
          searchParams.get("expireTime") || getCookie("ipoc_expireTime");
        //解决已登录账号复制链接在当前浏览器打开 展示全部权限的问题
        if (
          (urlAuthId && !cookieAuthId) ||
          (cookieAuthId && !storageSession().getItem<DataInfo>(sessionKey))
        ) {
          const authId = urlAuthId || cookieAuthId;
          setToken(authId);
          this.getUserDetail(authId).then((data: any) => {
            setLoginTime(loginTime, expireTime);
            setUserInfo(data?.Data);
            const params = getQueryMap(location.href);
            const page = params["page"] ? "#" + params["page"] : location.hash;
            delete params["authId"];
            delete params["key"];
            delete params["userId"];
            delete params["isAdmin"];
            delete params["page"];
            const newUrl = `${location.origin}${location.pathname}${params
              ? "?" +
              JSON.stringify(params)
                .replace(/["{}]/g, "")
                .replace(/:/g, "=")
                .replace(/,/g, "&")
                .replace(/[&=undefined#\/]/g, "")
              : ""
              }${page}`;
            location.reload();
            // // 替换历史记录项
            window.location.replace(newUrl);
          });
        } else {
          resolve("");
        }
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
