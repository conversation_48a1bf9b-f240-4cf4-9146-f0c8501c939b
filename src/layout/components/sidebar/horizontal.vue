<script setup lang="ts">
import Search from "../search/index.vue";
import Notice from "../notice/index.vue";
import SidebarItem from "./sidebarItem.vue";
import { isAllEmpty } from "@pureadmin/utils";
import { ref, nextTick, computed, reactive } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import { useTranslationLang } from "../../hooks/useTranslationLang";
import { usePermissionStoreHook } from "@/store/modules/permission";
import globalization from "@/assets/svg/globalization.svg?component";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import Setting from "@iconify-icons/ri/settings-3-line";
import Check from "@iconify-icons/ep/check";
import { getAssetsFile } from "@/utils/util";
import { jumpToCenter } from "@/utils/auth";
import type { FormInstance } from "element-plus";
import { SyncPassengerAlarmSetting, GetSetting, SetPassword } from "@/api/user";
import { ElMessage, ElMessageBox } from "element-plus";
const menuRef = ref();

const { t, route, locale, translationCh, translationEn } =
  useTranslationLang(menuRef);
const {
  title,
  logout,
  backTopMenu,
  onPanel,
  username,
  userAvatar,
  avatarsStyle,
  getDropdownItemStyle,
  getDropdownItemClass
} = useNav();

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

// 全局设置
const showSettingDialog = ref(false);
const settingDialogForm = ref<any>({});
const settingDialogFormRef = ref<FormInstance>();
const onSetting = () => {
  showSettingDialog.value = true;
  GetSetting({}).then(res => {
    let data = res.Data.PassengerAlarmItem;
    settingDialogForm.value.PreWarning = data?.PreWarning ?? 0;
    settingDialogForm.value.AlarmWarning = data?.AlarmWarning ?? 0;
  });
};
const settingDialogClose = () => {
  settingDialogForm.value = {};
  settingDialogFormRef.value.resetFields();
};
const setting_confirm = () => {
  let form = settingDialogForm.value;
  SyncPassengerAlarmSetting({
    PreWarning: parseInt(form.PreWarning),
    AlarmWarning: parseInt(form.AlarmWarning)
  }).then(res => {
    showSettingDialog.value = false;
    ElMessage.success("设置成功");
  });
};

// 密码修改弹窗
const validatePass = (rule, value, callback) => {
  if (
    !value ||
    value.length < 8 ||
    !value.match(/\d/) ||
    !value.match(/[a-z]/) ||
    !value.match(/[A-Z]/)
  ) {
    callback(new Error("密码必须包含大写字母、小写字母且数字不少于8位!"));
  } else {
    callback();
  }
};
const validateConfirmPassword = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== passwordDialog_form.value.NewPassword) {
    callback(new Error("两次输入不匹配!"));
  } else {
    callback();
  }
};
const passwordDialog_show = ref(false);
const passwordDialog_formRef = ref(null);
const passwordDialog_form = ref({
  OldPassword: undefined,
  NewPassword: undefined,
  ConfirmPassword: undefined
});
const passwordDialog_rules = reactive({
  OldPassword: [{ required: true, validator: validatePass, trigger: "blur" }],
  NewPassword: [{ required: true, validator: validatePass, trigger: "blur" }],
  ConfirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: "blur" }
  ]
});
const showUpdatePassWord = () => {
  passwordDialog_show.value = true;
};
const passwordDialog_close = () => {
  passwordDialog_formRef?.value?.clearValidate();
};
const passwordDialog_confirm = () => {
  // 校验表单, 更新密码
  passwordDialog_formRef.value.validate((valid, error) => {
    if (valid) {
      SetPassword({
        ...passwordDialog_form.value
      }).then(({ Code, Msg }) => {
        if (Code === "0") {
          ElMessage.success(Msg);
          passwordDialog_show.value = false;
        }
      });
    } else {
      return false;
    }
  });
};

nextTick(() => {
  menuRef.value?.handleResize();
});
</script>

<template>
  <div
    v-loading="usePermissionStoreHook().wholeMenus.length === 0"
    class="horizontal-header"
  >
    <div class="horizontal-header-left" @click="jumpToCenter">
      <!-- <img src="/logo.svg" alt="logo" /> -->
      <span>{{ title }}</span>
    </div>
    <!-- <el-menu
      router
      ref="menuRef"
      mode="horizontal"
      class="horizontal-header-menu"
      :default-active="defaultActive"
    >
      <sidebar-item
        v-for="route in usePermissionStoreHook().wholeMenus"
        :key="route.path"
        :item="route"
        :base-path="route.path"
      />
    </el-menu> -->
    <el-button class="logout-button" type="primary" @click="logout">
      退出登录
    </el-button>
    <!-- <div class="horizontal-header-right"> -->
    <!-- 菜单搜索 -->
    <!-- <Search /> -->
    <!-- 通知 -->
    <!-- <Notice id="header-notice" /> -->
    <!-- 国际化 -->
    <!-- <el-dropdown id="header-translation" trigger="click">
        <globalization
          class="navbar-bg-hover w-[40px] h-[48px] p-[11px] cursor-pointer outline-none"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'zh')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'zh')]"
              @click="translationCh"
            >
              <span class="check-zh" v-show="locale === 'zh'">
                <IconifyIconOffline :icon="Check" />
              </span>
              简体中文
            </el-dropdown-item>
            <el-dropdown-item
              :style="getDropdownItemStyle(locale, 'en')"
              :class="['dark:!text-white', getDropdownItemClass(locale, 'en')]"
              @click="translationEn"
            >
              <span class="check-en" v-show="locale === 'en'">
                <IconifyIconOffline :icon="Check" />
              </span>
              English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
    <!-- 退出登录 -->
    <!-- <div
        v-permission="'/ipoc/station/SyncPassengerAlarmSetting'"
        class="setting"
        @click="onSetting"
      > -->
    <!-- <img src="@/assets/img/setting.png" alt="" /> -->
    <!-- </div>
      <el-dropdown trigger="click">
        <span class="el-dropdown-link navbar-bg-hover"> -->
    <!-- <img :src="userAvatar" :style="avatarsStyle" /> -->
    <!-- <img :src="getAssetsFile('img/headSculpture.png')" /> -->
    <!-- <p v-if="username" class="dark:text-white">{{ username }}</p>
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <el-dropdown-item @click="showUpdatePassWord"
              >修改密码</el-dropdown-item
            >
            <el-dropdown-item @click="logout">
              <IconifyIconOffline
                :icon="LogoutCircleRLine"
                style="margin: 5px"
              />
              {{ t("buttons.hsLoginOut") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
    <!-- <span
        class="set-icon navbar-bg-hover"
        :title="t('buttons.hssystemSet')"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="Setting" />
      </span> -->
    <!-- </div> -->

    <!-- 弹框 -->
    <el-dialog
      title="设置"
      v-model="showSettingDialog"
      append-to-body
      class="dialogPublicCss"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="settingDialogClose"
      width="fit-content"
    >
      <div class="content">
        <el-form
          ref="settingDialogFormRef"
          size="public"
          label-position="top"
          :model="settingDialogForm"
        >
          <div class="formItemBox">
            <el-form-item label="预警人数" prop="PreWarning">
              <el-input-number
                style="width: 250px"
                v-model="settingDialogForm.PreWarning"
                placeholder="请输入"
                controls-position="right"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </div>
          <div class="formItemBox">
            <el-form-item label="告警人数" prop="AlarmWarning">
              <el-input-number
                style="width: 250px"
                v-model="settingDialogForm.AlarmWarning"
                placeholder="请输入"
                controls-position="right"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div>
          <el-button
            size="public"
            type="primary"
            @click="setting_confirm(settingDialogFormRef)"
            >保存</el-button
          >
          <el-button size="public" @click="showSettingDialog = false"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="passwordDialog_show"
      title="修改密码"
      :close-on-click-modal="false"
      append-to-body
      style="width: 550px"
      @closed="passwordDialog_close"
      class="dialogPublicCss"
    >
      <div>
        <el-form
          ref="passwordDialog_formRef"
          :model="passwordDialog_form"
          :rules="passwordDialog_rules"
          label-position="top"
          inline
        >
          <el-form-item
            label="原密码"
            prop="OldPassword"
            style="margin-right: auto"
          >
            <el-input
              v-model.trim="passwordDialog_form.OldPassword"
              type="password"
              autocomplete="new-password"
              name="OldPassword"
              placeholder="请输入原密码"
              style="width: 215px"
            />
          </el-form-item>
          <div style="width: 215px"></div>
          <el-form-item label="新密码" prop="NewPassword">
            <el-input
              v-model.trim="passwordDialog_form.NewPassword"
              type="password"
              autocomplete="new-password"
              name="NewPassword"
              placeholder="请输入新密码"
              style="width: 215px"
            />
          </el-form-item>
          <el-form-item label="确认新密码" prop="ConfirmPassword">
            <el-input
              v-model.trim="passwordDialog_form.ConfirmPassword"
              type="password"
              autocomplete="new-password"
              name="ConfirmPassword"
              placeholder="请再次输入新密码"
              style="width: 215px"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="passwordDialog_confirm"
          >确定</el-button
        >
        <el-button @click="passwordDialog_show = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.logout-button {
  margin-left: auto;
}
.setting {
  height: 25px;
  width: 25px;
  img {
    height: 100%;
    width: 100%;
    cursor: pointer;
  }
}
:deep(.el-loading-mask) {
  opacity: 0.45;
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.logout {
  max-width: 200px;

  ::v-deep(.el-dropdown-menu__item) {
    display: flex;
    flex-wrap: wrap;
    min-width: 100%;
  }
}

:deep(.el-input) {
  width: 100%;
}
</style>
