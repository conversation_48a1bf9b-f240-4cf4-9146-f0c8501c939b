<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { ElConfigProvider } from "element-plus";
// import zhCn from "element-plus/lib/locale/lang/zh-cn";
// import en from "element-plus/lib/locale/lang/en";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import en from "element-plus/dist/locale/en.mjs";
import { ReDialog } from "@/components/ReDialog";
import { useUserStoreHook } from "@/store/modules/user";
import { useCorporationStoreHook } from "@/store/modules/corporation";
import { startAuthIdCheckTimerTask } from "@/utils/auth";

const userStore = useUserStoreHook();
const corporationStore = useCorporationStoreHook();

// 初始化用户状态（从sessionStorage恢复）
userStore.initUserState();

userStore.ssoLogin().then(() => {
  // startAuthIdCheckTimerTask();
  corporationStore.getCorporationList();
});

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog
  },
  computed: {
    currentLocale() {
      return this.$storage.locale?.locale === "zh" ? zhCn : en;
    }
  }
});
</script>
