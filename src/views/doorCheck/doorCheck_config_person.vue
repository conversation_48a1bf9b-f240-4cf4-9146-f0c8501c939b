<template>
  <div class="page-container statTablePagePublicCss">
    <!-- 门检管理员排班 -->
    <!-- 页面内容 -->
    <div class="p_contentBox">
      <!-- 顶部栏 -->
      <div class="p_head">
        <el-form
          size="public"
          :model="queryForm"
          ref="queryFormRef"
          :inline="true"
          @keyup.enter="handleQuery"
          class="p_queryForm"
        >
          <el-select
            v-model="queryForm.CorporationId"
            placeholder="请选择机构"
            clearable
            style="width: 180px; margin-right: 10px"
          >
            <el-option
              v-for="item in corporationList"
              :key="item.IdStr"
              :value="item.IdStr"
              :label="item.Name"
            />
          </el-select>
          <el-date-picker
            size="public"
            v-model="queryForm.Date"
            type="month"
            placeholder="请选择月份"
            format="YYYY/MM"
            style="width: 150px; margin-right: 10px"
          />
          <el-button type="primary" @click="handleQuery">查询</el-button>
        </el-form>
        <div class="p_headBtnBox">
          <el-button size="public" @click="handleImport">导入</el-button>
          <el-button size="public" @click="handleExport">导出</el-button>
        </div>
      </div>
      <!-- 主体 -->
      <div class="p_body" v-loading="bodyloading">
        <el-calendar ref="calendar" v-model="calendar_value">
          <!-- 日历头部栏 -->
          <template #header="{ date }">
            <span>{{ date }}</span>
            <el-button-group>
              <el-button size="small" @click="selectDate('prev-year')">
                上一年
              </el-button>
              <el-button size="small" @click="selectDate('prev-month')">
                上一月
              </el-button>
              <el-button size="small" @click="selectDate('today')"
                >当月</el-button
              >
              <el-button size="small" @click="selectDate('next-month')">
                下一月
              </el-button>
              <el-button size="small" @click="selectDate('next-year')">
                下一年
              </el-button>
            </el-button-group>
          </template>
          <!-- 日历单元格 -->
          <template #date-cell="{ data }">
            <div class="calendarCell" @click.stop="">
              <div class="head">
                <span class="date">{{
                  data.day.split("-").slice(1).join("-")
                }}</span>
                <p class="btnBox">
                  <el-icon
                    v-permission="'/ipoc/doorcheck/SyncInspector'"
                    v-show="queryReq.CorporationId"
                    size="17"
                    color="#2492EB"
                  >
                    <Edit @click="calendar_set(data)" />
                  </el-icon>
                </p>
              </div>
              <div class="list">
                <span
                  v-for="(item, i) in calendarPersonData[data.day]?.Items ?? []"
                  :key="i"
                  >{{ item.Name }}</span
                >
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </div>
    <!-- 管理员设置弹框 -->
    <el-dialog
      title="选择管理员"
      v-model="setDialog_show"
      append-to-body
      class="setDialog dialogPublicCss"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="setDialog_close"
      width="fit-content"
    >
      <div class="content">
        <ReTree
          :type="6"
          :showCheck="true"
          v-show="true"
          ref="setDialog_tree"
          @checkedTreeNode="setDialog_treeChecked"
        />
      </div>
      <template #footer>
        <div>
          <el-button size="public" type="primary" @click="setDialog_confirm"
            >确定</el-button
          >
          <el-button size="public" @click="setDialog_show = false"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 导入弹窗 -->
    <el-dialog
      title="导入"
      v-model="importDialog_show"
      append-to-body
      class="importDialog dialogPublicCss"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="importDialog_cancel"
      width="400px"
    >
      <div class="uploadBox">
        <el-upload
          class="elUpload"
          drag
          action="#"
          :auto-upload="true"
          :http-request="importDialog_upload"
          :multiple="false"
          :show-file-list="false"
        >
          <el-icon style="font-size: 67px; color: #c0c4cc">
            <UploadFilled />
          </el-icon>
          <div
            v-show="importDialog_fileName"
            class="el-upload__text"
            v-html="importDialog_fileName"
          />
          <div
            v-show="!importDialog_fileName"
            class="el-upload__text"
            v-html="'将文件拖到此处，或<em>点击上传</em>'"
          />
          <template #tip>
            <div class="el-upload__tip">支持xlsx文件</div>
          </template>
        </el-upload>
        <div class="btns" style="width: 100%">
          <div>
            <el-button type="primary" @click="handleImport_download">
              导入模板下载
            </el-button>
          </div>
          <div>
            <el-button type="primary" @click="handleImport_confirm"
              >确定</el-button
            >
            <el-button @click="importDialog_show = false">取消</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue";
import ReTree from "@/components/ReTree/index.vue";
import {
  ListInspector,
  SyncInspector,
  ImportInspector
} from "@/api/doorCheck/list";
import { ElMessage, ElMessageBox } from "element-plus";
import { exportXlsx } from "@/utils/xlsx";
import JSONbig from "json-bigint";
import { TreeType, TreeItem, getTreeList } from "@/api/center";

defineOptions({
  name: "doorCheckConfigPerson"
});

// onMounted(() => {
//   // 获取机构列表
//   getTreeList({
//     TreeType: 1,
//     Offset: 0,
//     Limit: 99999999,
//     Order: "asc",
//     IsLineAttribute: 0
//   }).then(res => {
//     const data = res?.Data?.Items ?? [];
//     data?.forEach((item: any) => {
//       item.IdStr = item.Id.toString();
//       item.ParentIdStr = item.ParentId.toString();
//     });
//     // corporationList.value = data.filter(v => v.Type == 2);
//     corporationList.value = data;
//   });
// });

// 机构列表（下拉选择，仅展示“公司”类型机构）
const corporationList = ref<any>([]);

const bodyloading = ref(false);

// 查询-查询条件（动态）
const queryForm = reactive<any>({
  CorporationId: "",
  Date: new Date(new Date().getFullYear() + "/" + (new Date().getMonth() + 1)) // 默认为当月
});
// 查询-上次查询的条件
let queryReq = reactive<any>({});
// 查询-点击查询
const handleQuery = async () => {
  if (!queryForm.CorporationId) {
    ElMessage.warning("请选择机构");
    return;
  }
  if (!queryForm.Date) {
    ElMessage.warning("请选择月份");
    return;
  }

  bodyloading.value = true;

  let monthDate = new Date(
    new Date(
      new Date(queryForm.Date).getFullYear() +
        "/" +
        (new Date(queryForm.Date).getMonth() + 1)
    )
  );

  let date = new Date(monthDate);
  date.setMonth(date.getMonth() + 1);
  date.setSeconds(-1);

  queryReq = {
    CorporationId: queryForm.CorporationId,
    StartAt: monthDate.getTime() / 1000,
    EndAt: date.getTime() / 1000
  };

  // 清空数据
  calendarPersonData.value = [];

  // 联动日历日期
  calendar_value.value = new Date(monthDate);

  let res = await ListInspector({
    ...queryReq,
    CorporationId: JSONbig.parse(queryReq.CorporationId)
  }).finally(() => {
    setTimeout(() => {
      bodyloading.value = false;
    }, 500);
  });
  let list = res.Data?.Items ?? [];

  // 测试假数据
  // list = [
  //   {
  //     DateStr: "2024-07-01",
  //     Items: [
  //       { UserId: 710, Name: "人员1", RecordId: 1 },
  //       { UserId: 787, Name: "人员2", RecordId: 2 },
  //       { UserId: 3, Name: "人员3", RecordId: 3 },
  //       { UserId: 4, Name: "人员4", RecordId: 4 },
  //       { UserId: 5, Name: "人员5", RecordId: 5 },
  //       { UserId: 6, Name: "人员6", RecordId: 6 }
  //     ]
  //   },
  //   {
  //     DateStr: "2024-08-02",
  //     Items: [
  //       { UserId: 1, Name: "人员1", RecordId: 1 },
  //       { UserId: 2, Name: "人员2", RecordId: 2 },
  //       { UserId: 3, Name: "人员3", RecordId: 3 }
  //     ]
  //   },
  //   {
  //     DateStr: "2024-07-03",
  //     Items: [{ UserId: 1, Name: "人员1", RecordId: 1 }]
  //   },
  //   {
  //     DateStr: "2024-07-04",
  //     Items: null
  //   }
  // ];

  let data = {};
  list.forEach(v => {
    data[v.DateStr] = v;
  });

  calendarPersonData.value = data;
};

// 导出-点击导出
const handleExport = () => {
  if (!queryReq.CorporationId) {
    ElMessage.warning("请先查询");
    return;
  }
  let personDatas = Object.values(calendarPersonData.value);
  const sheetList = [
    [
      "日期",
      "管理员1",
      "手机1",
      "管理员2",
      "手机2",
      "管理员3",
      "手机3",
      "管理员4",
      "手机4",
      "管理员5",
      "手机5",
      "管理员6",
      "手机6"
    ]
  ];
  personDatas.forEach(data => {
    let row = [data.DateStr];
    data.Items.forEach(item => {
      row.push(item.Name || "");
      row.push(item.Phone || "");
    });
    sheetList.push(row);
  });

  let dateStr = new Date(queryReq.StartAt * 1000)
    .toLocaleDateString()
    .replaceAll("/", "-");

  exportXlsx({
    data: sheetList,
    jtsOpts: {
      skipHeader: true
    },
    sheetName: "门检管理员配置",
    fileName: `门检管理员配置_${dateStr}.xlsx`
  });
};

// 导入-弹框是否显示
const importDialog_show = ref(false);
// 导入-文件名
const importDialog_fileName = ref("");
// 导入-文件对象
let importDialog_file = null;
// 导入-点击导入
const handleImport = () => {
  if (!queryForm.CorporationId) {
    ElMessage.warning("请先选择机构");
  } else {
    importDialog_show.value = true;
  }
};
// 导入-取消
const importDialog_cancel = () => {
  importDialog_fileName.value = false;
  importDialog_file = null;
};
// 导入-确认
const handleImport_confirm = () => {
  if (!importDialog_file) {
    ElMessage.warning("请上传文件");
    return;
  } else {
    let reader = new FileReader();
    reader.readAsDataURL(importDialog_file);
    reader.onload = () => {
      let fileData = reader.result;
      ImportInspector({
        CorporationId: JSONbig.parse(queryForm.CorporationId),
        FileData: fileData.split(";base64,")[1]
      }).then(res => {
        let failItems = res.Data?.FailItems ?? [];
        if (failItems.length) {
          let msg = "";
          failItems.forEach(v => {
            msg += `第${v.RowIndex}行，第${v.ColumnIndex}列，${v.Error}；`;
          });
          ElMessageBox.confirm(msg, "导入失败", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info"
          }).then(async () => {});
        } else {
          ElMessage.success("导入成功");
          importDialog_show.value = false;
        }
      });
    };
  }
};
// 导入-下载模板
const handleImport_download = () => {
  const a = document.createElement("a");
  a.href = `/static/门检管理员导入.xlsx`;
  a.download = `门检管理员导入.xlsx`;
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.remove();
};
// 导入-上传文件
const importDialog_upload = ({ file }) => {
  if (
    file.type !==
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    ElMessage.error("文件格式有误");
    return;
  }
  importDialog_fileName.value = file.name;
  importDialog_file = file;
};

// 日历-日期人员设置数据
const calendarPersonData = ref<any>({});
// 日历-组件对象
const calendar = ref<null>();
// 日历-当前日历日期（默认与查询条件相同）
const calendar_value = ref(new Date(queryForm.Date));
// 日历-快捷切换日期（上一年、上一月、今天、下一月、下一年）
const selectDate = (val: null) => {
  console.log(calendar_value.value);
  if (!calendar.value) return;
  // 修改日历日期
  calendar.value.selectDate(val);
  // 联动查询日期
  queryForm.Date = new Date(calendar_value.value);
  // 重新查询数据
  handleQuery();
};
// 日历-点击设置
const calendar_set = cellData => {
  setDialog_cellBaseData = calendarPersonData.value[cellData.day] || {
    DateStr: cellData.day,
    Items: []
  };
  setDialog_show.value = true;

  setTimeout(() => {
    let defCheckedNodeKeys =
      setDialog_cellBaseData?.Items?.map(v => TreeItem.User + "_" + v.UserId) ??
      [];
    setDialog_tree.value.treeRef.setCheckedKeys(defCheckedNodeKeys, false);
  }, 1000);
};

// 人员设置-弹框是否显示
const setDialog_show = ref(false);
// 人员设置-人员树
const setDialog_tree = ref(null);
// 人员设置-日历格子原数据
let setDialog_cellBaseData = null;
// 人员设置-取消
const setDialog_close = () => {
  setDialog_tree.value.treeRef.setCheckedKeys([], false);
};
// 人员设置-确定
const setDialog_treeChecked = () => {
  let checkedNodes = setDialog_tree.value.treeRef.getCheckedNodes();
  let userNodes = checkedNodes.filter(v => v.Type == TreeType.User);
  if (userNodes.length > 6) {
    ElMessage.warning("最多配置6名管理员");
  }
};
const setDialog_confirm = async () => {
  let checkedNodes = setDialog_tree.value.treeRef.getCheckedNodes();
  let userNodes = checkedNodes.filter(v => v.Type == TreeType.User);
  if (userNodes.length > 6) {
    ElMessage.warning("最多配置6名管理员");
    return;
  }
  let baseUsers = setDialog_cellBaseData?.Items ?? [];

  // 获取新增人员
  let addUserNodes = userNodes.filter(
    node => !baseUsers.find(user => user.UserId == node.Id)
  );

  // 获取删除人员
  let delUsers = baseUsers.filter(
    user => !userNodes.find(node => node.Id == user.UserId)
  );

  // 将新增人员按机构分组
  // let corpAddUserNodes = [];
  // addUserNodes.forEach(node => {
  //   let corp = corpAddUserNodes.find(v => v.id == node.AttachCorporationId);
  //   if (corp) {
  //     corp.nodes.push(node);
  //   } else {
  //     corpAddUserNodes.push({
  //       id: node.AttachCorporationId,
  //       nodes: [node]
  //     });
  //   }
  // });

  await SyncInspector({
    // AddItems: corpAddUserNodes.map(corp => {
    //   return {
    //     CorporationId: JSONbig.parse(corp.id),
    //     UserItems: [
    //       {
    //         DateStr: setDialog_cellBaseData.DateStr,
    //         UserIds: corp.nodes.map(node => node.Id)
    //       }
    //     ]
    //   };
    // }),
    AddItems: [
      {
        CorporationId: JSONbig.parse(queryReq.CorporationId),
        UserItems: [
          {
            DateStr: setDialog_cellBaseData.DateStr,
            UserIds: addUserNodes.map(node => node.Id)
          }
        ]
      }
    ],
    DelItems: delUsers.map(user => JSONbig.parse(user.RecordId))
  });
  ElMessage.success("设置成功");
  setDialog_show.value = false;

  // 重新查询数据
  handleQuery();
};
</script>
<style>
.setDialog .el-dialog__body {
  padding: unset;
}
</style>
<style lang="scss" scoped>
.page-container {
  .p_body {
    padding: unset;
    display: flex;
  }
}
.calendarCell {
  height: 100%;
  width: 100%;
  padding: 8px;
  .head {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    .date {
      font-size: 15px;
    }
    .btnBox {
      display: none;
      // display: flex;
      align-items: center;
      span {
        display: block;
        border: 1px solid #ccc;
        color: #ccc;
        width: 18px;
        height: 18px;
        line-height: 16px;
        text-align: center;
        border-radius: 3px;
        margin-left: 5px;
      }
      .add {
        border: 1px solid #2492eb;
        color: #2492eb;
      }
    }
  }
  .list {
    display: flex;
    flex-wrap: wrap;
    // align-items: center;
    // justify-content: space-between;
    height: calc(100% - 30px);
    span {
      display: block;
      white-space: nowrap;
      width: calc(33% - 4px);
      margin-left: 4px;
      background: #ecf0f1;
      font-size: 11px;
      text-align: center;
      border-radius: 3px;
      padding: 2px 2px;
      height: 21px;
      margin-bottom: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.current .calendarCell:hover {
  .head .btnBox {
    display: flex;
  }
}
:deep(.el-calendar-day) {
  padding: unset;
}
.current .calendarCell .list span {
  background: #ddf8ff;
  outline: 1px solid #9febff;
}

.setDialog {
  .content {
    width: 330px;
    height: 400px;
  }
}

.uploadBox {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 20px;
  .elUpload {
    width: 100%;
    .el-upload {
      width: 100%;
      .el-upload-dragger {
        width: 100%;
      }
    }
    .el-upload__tip {
      color: red;
    }
  }
  .btns {
    display: flex;
    justify-content: space-between;
  }
}
</style>
