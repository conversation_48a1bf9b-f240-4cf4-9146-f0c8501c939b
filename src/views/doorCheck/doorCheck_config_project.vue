<template>
  <div class="page-container statTablePagePublicCss">
    <!-- 门检项目配置 -->
    <!-- 页面内容 -->
    <div class="p_contentBox">
      <!-- 顶部栏 -->
      <div class="p_head">
        <el-form
          size="public"
          :model="queryForm"
          ref="queryFormRef"
          :inline="true"
          @keyup.enter="handleQuery"
          class="p_queryForm"
        >
          <el-select
            v-model="queryForm.CorporationId"
            placeholder="请选择机构"
            clearable
            style="width: 180px; margin-right: 10px"
          >
            <el-option
              v-for="item in corporationList"
              :key="item.IdStr"
              :value="item.IdStr"
              :label="item.Name"
            />
          </el-select>
          <el-button type="primary" @click="handleQuery">查询</el-button>
        </el-form>
        <div class="p_headBtnBox"></div>
      </div>
      <!-- 主体 -->
      <div class="p_body">
        <!-- 检查类型 -->
        <div class="projectTypeList">
          <div
            v-for="(item, i) in projectType_list"
            :key="i"
            :class="`projectTypeItem ${
              projectType.Type == item.Type && projectType.Id == item.Id
                ? 'selected'
                : ''
            }`"
            @click="projectType_change(item)"
          >
            <span>{{ item.Name }}</span>
            <div class="btnBox" v-show="item.Type == 99">
              <el-icon
                v-permission="'/ipoc/doorcheck/SyncProjectType'"
                size="17"
                color="#2492EB"
              >
                <Edit @click="projectType_edit(item)" />
              </el-icon>
              <el-icon
                v-permission="'/ipoc/doorcheck/SyncProjectType'"
                size="17"
                color="#D0021B"
              >
                <Delete @click="projectType_del(item)" />
              </el-icon>
            </div>
          </div>
          <el-button
            v-show="queryReq"
            v-permission="'/ipoc/doorcheck/SyncProjectType'"
            class="addBtn"
            plain
            @click="projectType_add"
            >+新增检查类型</el-button
          >
        </div>
        <!-- 检查项目 -->
        <div class="projectList">
          <div class="head">
            <el-button
              v-show="projectType.Type"
              v-permission="'/ipoc/doorcheck/AddProject'"
              @click="project_add"
              >新增检查项目</el-button
            >
          </div>
          <!-- 收缩栏 -->
          <div class="collapseList">
            <div
              :class="`projectItem ${item.collapse?.length ? 'expend' : ''}`"
              v-for="(item, i) in project_list"
              :key="i"
            >
              <el-collapse
                v-model="item.collapse"
                @change="project_collapseChange($event, item)"
              >
                <el-collapse-item name="1">
                  <template #title>
                    <!-- 头部栏 -->
                    <div class="header">
                      <span class="seq">{{ item.seq }}</span>
                      <span class="title">{{ item.title }}</span>
                      <div class="imgBox">
                        <el-image :src="item.headImgUrl">
                          <template #error>
                            <div class="image-error">
                              <el-icon size="20"><Picture /></el-icon>
                            </div>
                          </template>
                        </el-image>
                      </div>
                      <div class="labelList">
                        <span
                          class="labelItem"
                          v-for="(label, j) in item.labelList"
                          :key="j"
                          v-show="label.show"
                          :style="`color:${label.fontColor};border:1px solid ${label.fontColor};background:${label.bgColor}`"
                          >{{ label.name }}</span
                        >
                      </div>
                      <div class="btnBox" v-show="!item.openEdit">
                        <el-icon
                          v-permission="'/ipoc/doorcheck/EditProject'"
                          size="20"
                          color="#2492EB"
                        >
                          <Edit @click.stop="project_edit(item)" />
                        </el-icon>
                        <el-icon
                          v-permission="'/ipoc/doorcheck/DelProject'"
                          size="20"
                          color="#D0021B"
                        >
                          <Delete @click.stop="project_del(item)" />
                        </el-icon>
                      </div>
                    </div>
                  </template>
                  <div class="content">
                    <!-- 详情表单 -->
                    <div class="formBox">
                      <el-form
                        class="formList"
                        :ref="e => (project_refs[`projectRef${i}`] = e)"
                        size="public"
                        label-position="top"
                        :model="item"
                      >
                        <el-form-item
                          v-if="projectType.Type != 2"
                          :rules="[
                            { required: true, message: '请输入项目名称' }
                          ]"
                          label="项目名称"
                          prop="Name"
                        >
                          <el-input
                            :disabled="!item.openEdit"
                            v-model="item.Name"
                            placeholder="请输入"
                          />
                        </el-form-item>
                        <!-- 设备种类下列列表仅在所选的检查类型为“设备”时显示 -->
                        <el-form-item
                          v-if="projectType.Type == 2"
                          :rules="[
                            { required: true, message: '请选择设备种类' }
                          ]"
                          label="设备种类"
                          prop="CategoryId"
                        >
                          <el-select
                            :disabled="!item.openEdit"
                            v-model="item.CategoryId"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in dictList"
                              :key="item.Id"
                              :value="item.Id"
                              :label="item.DictKey"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请输入显示顺序' }
                          ]"
                          label="显示顺序"
                          prop="Sort"
                        >
                          <el-input-number
                            :disabled="!item.openEdit"
                            v-model="item.Sort"
                            controls-position="right"
                            :min="0"
                            placeholder="请输入"
                          />
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请选择异常处理' }
                          ]"
                          label="异常处理"
                          prop="DealAbNormalMethod"
                        >
                          <el-select
                            :disabled="!item.openEdit"
                            v-model="item.DealAbNormalMethod"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in DealAbNormalMethodList"
                              :key="item.value"
                              :value="item.value"
                              :label="item.label"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请选择异常出场' }
                          ]"
                          label="异常出场"
                          prop="OutDepotAbNormal"
                        >
                          <el-radio-group
                            :disabled="!item.openEdit"
                            v-model="item.OutDepotAbNormal"
                          >
                            <el-radio :label="1">允许</el-radio>
                            <el-radio :label="2">不允许</el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请选择门检类型' }
                          ]"
                          label="门检类型"
                          prop="CheckTypes"
                        >
                          <el-checkbox-group
                            :disabled="!item.openEdit"
                            v-model="item.CheckTypes"
                          >
                            <el-checkbox :label="1">回场</el-checkbox>
                            <el-checkbox :label="2">出场</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请选择检测时效' }
                          ]"
                          label="检测时效"
                          prop="CheckTimeType"
                        >
                          <el-select
                            :disabled="!item.openEdit"
                            v-model="item.CheckTimeType"
                            placeholder="请选择"
                            @change="project_checkTimeTypeChange(item)"
                          >
                            <el-option
                              v-for="item in CheckTimeTypeList"
                              :key="item.value"
                              :value="item.value"
                              :label="item.label"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            {
                              required: item.CheckTimeType == 2,
                              message: '请选择检测时段'
                            }
                          ]"
                          label="检测时段"
                          prop="CheckAt"
                        >
                          <el-date-picker
                            :disabled="
                              !item.openEdit || item.CheckTimeType == 1
                            "
                            v-model="item.CheckAt"
                            type="daterange"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            size="public"
                            format="YYYY/MM/DD"
                            value-format="x"
                          />
                        </el-form-item>
                        <el-form-item
                          :rules="[
                            { required: true, message: '请选择项目图标' }
                          ]"
                          label="项目图标"
                          prop="IconFileId"
                        >
                          <div class="uploadImgBox">
                            <el-upload
                              class="elUpload"
                              accept="image/*"
                              :auto-upload="false"
                              :show-file-list="false"
                              :on-change="file => project_uploadImg(file, item)"
                            >
                              <el-image :src="item.IconFileUrl">
                                <template #error>
                                  <el-icon size="20"><Picture /></el-icon>
                                </template>
                              </el-image>
                              <div
                                v-show="item.IconFileUrl"
                                class="imgDel"
                                @click.stop="project_delImg(item)"
                              >
                                +
                              </div>
                            </el-upload>
                            <span class="tip">*png、jpg格式，小于5MB</span>
                          </div>
                        </el-form-item>
                      </el-form>
                      <!-- 分割线 -->
                      <div class="line"></div>

                      <el-form
                        class="formList"
                        size="public"
                        label-position="top"
                        :model="item"
                      >
                        <el-form-item label="正常">
                          <el-checkbox
                            :disabled="!item.openEdit"
                            v-model="item.IsDescNormalBool"
                            >显示文本描述框</el-checkbox
                          >
                        </el-form-item>
                        <el-form-item label="异常" class="abNormalBox">
                          <div class="checkBox">
                            <el-checkbox
                              :disabled="!item.openEdit"
                              v-model="item.IsDescAbNormalBool"
                              >显示文本描述框</el-checkbox
                            >
                            <el-checkbox
                              :disabled="!item.openEdit"
                              v-model="item.IsAttachmentAbNormalBool"
                              :label="1"
                              >必传附件</el-checkbox
                            >
                          </div>
                          <div class="abNormalList">
                            <div
                              class="abNormalItem"
                              v-for="(abNormal, k) in item.AbNormalItems"
                              :key="k"
                            >
                              <el-input
                                :disabled="!item.openEdit"
                                v-model="abNormal.Name"
                                placeholder="请输入"
                              />
                              <el-icon
                                size="17"
                                v-show="
                                  item.openEdit &&
                                  k == item.AbNormalItems?.length - 1
                                "
                                color="#2492EB"
                              >
                                <CirclePlus @click.stop="abNormal_add(item)" />
                              </el-icon>
                              <el-icon
                                size="17"
                                v-show="
                                  item.openEdit &&
                                  1 < item.AbNormalItems?.length
                                "
                                color="#D0021B"
                              >
                                <Delete
                                  @click.stop="abNormal_del(item, abNormal, k)"
                                />
                              </el-icon>
                            </div>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                    <!-- 编辑按钮 -->
                    <div v-show="item.openEdit" class="editBtns">
                      <el-button
                        type="primary"
                        @click="
                          project_confirm(item, project_refs[`projectRef${i}`])
                        "
                        >保存</el-button
                      >
                      <el-button @click="project_cancel(item)">取消</el-button>
                    </div>
                    <!-- 编辑遮罩 -->
                    <div v-show="!item.openEdit" class="editMask"></div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 检查类型-新增编辑弹框 -->
    <el-dialog
      :title="projectType_addEditDialog_title"
      v-model="projectType_addEditDialog_show"
      append-to-body
      class="addEditDialog dialogPublicCss"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="projectType_addEditDialog_close"
      width="fit-content"
    >
      <div class="content">
        <el-form
          ref="projectType_addEditDialog_formRef"
          size="public"
          label-position="top"
          :model="projectType_addEditDialog_form"
        >
          <div class="formItemBox">
            <el-form-item
              :rules="[
                {
                  required: true,
                  transform: value => value && value.trim(),
                  message: '请输入检查类型名称'
                }
              ]"
              label="检查类型名称"
              prop="Name"
            >
              <el-input
                v-model="projectType_addEditDialog_form.Name"
                placeholder="请输入名称"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div>
          <el-button
            size="public"
            type="primary"
            @click="
              projectType_addEditDialog_confirm(
                projectType_addEditDialog_formRef
              )
            "
            >确定</el-button
          >
          <el-button
            size="public"
            @click="projectType_addEditDialog_show = false"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue";
import {
  ListProjectType,
  SyncProjectType,
  ListProject,
  EditProject,
  AddProject,
  DelProject,
  ListDict
} from "@/api/doorCheck/list";
import { ElMessage, ElMessageBox } from "element-plus";
import { exportXlsx } from "@/utils/xlsx";
import JSONbig from "json-bigint";
import {
  DealAbNormalMethodList,
  getDealAbNormalMethodStr,
  CheckTimeTypeList,
  getCheckTimeTypeStr
} from "@/utils/code";
import { FileUploadSlice } from "@/utils/file";
import { getTreeList } from "@/api/center";

defineOptions({
  name: "doorCheckConfigProject"
});

// onMounted(() => {
//   // 获取机构列表
//   getTreeList({
//     TreeType: 1,
//     Offset: 0,
//     Limit: 99999999,
//     Order: "asc",
//     IsLineAttribute: 0
//   }).then(res => {
//     const data = res?.Data?.Items;
//     data?.forEach((item: any) => {
//       item.IdStr = item.Id.toString();
//       item.ParentIdStr = item.ParentId.toString();
//     });
//     corporationList.value = data.filter(v => v.Type == 2);
//   });
// });

// 设备种类列表
const dictList = ref([]);

// 机构列表（下拉选择，仅展示“公司”类型机构）
const corporationList = ref([]);

// 查询-查询条件（动态）
const queryForm = reactive<any>({
  CorporationId: ""
});
// 查询-上次查询的条件
let queryReq = reactive<any>(null);
// 查询-点击查询
const handleQuery = async () => {
  if (!queryForm.CorporationId) {
    ElMessage.warning("请选择机构");
    return;
  }

  queryReq = { CorporationId: queryForm.CorporationId };

  // 获取设备种类列表
  ListDict({
    ModuleType: 0x800,
    CorporationId: JSONbig.parse(queryReq.CorporationId),
    Type: 1,
    DictType: 14
  }).then(res => {
    let data = res.Data?.Items ?? [];
    data.forEach(v => {
      v.Id = v.Id.toString();
    });
    dictList.value = data;
  });

  // 查询检查类型列表
  projectType_getList(() => {
    // 默认选中首个检查类型
    projectType_change(projectType_list.value[0]);
  });
};

// 检查类型-当前所选类型
const projectType = ref<any>({});
// 检查类型-列表数据
const projectType_list = ref<any>([]);
// 检查类型-获取列表数据
const projectType_getList = async callback => {
  const res = await ListProjectType({
    CorporationId: JSONbig.parse(queryReq.CorporationId)
  }).finally(() => {});
  projectType_list.value = res?.Data?.Items ?? [];
  if (callback) callback();
};
// 检查类型-切换类型
const projectType_change = async item => {
  projectType.value = item || {};
  // 查询检查项目列表
  project_getList();
};
// 检查类型-新增类型
const projectType_add = () => {
  projectType_addEditDialog_title.value = "新增检查类型";
  projectType_addEditDialog_show.value = true;
};
// 检查类型-编辑类型
const projectType_edit = item => {
  projectType_addEditDialog_form.value = JSON.parse(JSON.stringify(item));
  projectType_addEditDialog_title.value = "编辑检查类型";
  projectType_addEditDialog_show.value = true;
};
// 检查类型-删除类型
const projectType_del = item => {
  ElMessageBox.confirm(`确定删 ${item.Name} 检查类型?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info"
  }).then(async () => {
    await SyncProjectType({
      CorporationId: JSONbig.parse(queryReq.CorporationId),
      DelItems: [JSONbig.parse(item.Id)]
    });
    ElMessage.success("删除成功");
    projectType_getList();
  });
};
// 检查类型-新增/编辑弹框
const projectType_addEditDialog_show = ref(false);
const projectType_addEditDialog_title = ref("");
const projectType_addEditDialog_form = ref({});
const projectType_addEditDialog_formRef = ref(null);
// 检查类型-新增/编辑弹框-确认
const projectType_addEditDialog_confirm = (formEl: any) => {
  if (!formEl) return;
  formEl.validate(async (valid: any) => {
    if (valid) {
      const formData = projectType_addEditDialog_form.value;
      if (!formData.Id) {
        await SyncProjectType({
          CorporationId: JSONbig.parse(queryReq.CorporationId),
          AddItems: [{ Type: 99, Name: formData.Name }]
        });
        projectType_addEditDialog_show.value = false;
        ElMessage.success("新增成功");
        projectType_getList();
      } else {
        await SyncProjectType({
          CorporationId: JSONbig.parse(queryReq.CorporationId),
          EditItems: [
            { Type: 99, Name: formData.Name, Id: JSONbig.parse(formData.Id) }
          ]
        });
        projectType_addEditDialog_show.value = false;
        ElMessage.success("编辑成功");
        projectType_getList();
      }
    }
  });
};
// 检查类型-新增/编辑弹框-关闭
const projectType_addEditDialog_close = () => {
  projectType_addEditDialog_form.value = {};
  projectType_addEditDialog_formRef.value.resetFields();
};

// 检查项目-列表数据
const project_list = ref<any>([]);
let project_list_base = [];
// 检查项目-表单对象
const project_refs = ref({});
// 检查项目-获取列表数据
const project_getList = async () => {
  const res = await ListProject({
    CorporationId: JSONbig.parse(queryReq.CorporationId),
    ProjectTypeId: JSONbig.parse(projectType.value.Id),
    ProjectType: projectType.value.Type
  }).finally(() => {});
  let data = res?.Data?.Items ?? [];

  // 测试数据
  // let data = [
  //   {
  //     Id: 1,
  //     Name: "项目1",
  //     CategoryId: 1,
  //     Sort: 1,
  //     IconFileUrl:
  //       "https://img2.baidu.com/it/u=3880877693,3055552033&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1721408400&t=c8f13ce808db7583c048f447b9ab857c",
  //     IconFileId: 1,
  //     DealAbNormalMethod: 1, // 异常处理: 1:修理厂修理;2:车队处理;3:设备工单
  //     IsAttachmentAbNormal: 1, // 门检项目-异常是否上传附件 1:是;2:否
  //     OutDepotAbNormal: 1, // 异常出场: 1:允许; 2:不允许
  //     CheckType: 1, // 门检类型: 1:回场;2:出场
  //     CheckTimeType: 1, // 检测时效: 1:常态化;2:临时检测
  //     CheckStartAt: new Date().getTime() / 1000,
  //     CheckEndAt: new Date().getTime() / 1000,
  //     IsDescNormal: 1, // 正常是否必传描述 1:是;2:否
  //     IsDescAbNormal: 1, // 异常是否必传描述 1:是;2:否
  //     // 异常标签
  //     AbNormalItems: [
  //       {
  //         Id: 1, // 异常标签Id
  //         Name: "标签1" // 异常标签名称
  //       },
  //       {
  //         Id: 2, // 异常标签Id
  //         Name: "标签2" // 异常标签名称
  //       }
  //     ]
  //   },
  //   {
  //     Id: 2,
  //     Name: "项目2",
  //     CategoryId: 2,
  //     Sort: 2,
  //     IconFileUrl: "",
  //     IconFileId: 0,
  //     DealAbNormalMethod: 2, // 异常处理: 1:修理厂修理;2:车队处理;3:设备工单
  //     IsAttachmentAbNormal: 2, // 门检项目-异常是否上传附件 1:是;2:否
  //     OutDepotAbNormal: 2, // 异常出场: 1:允许; 2:不允许
  //     CheckType: 2, // 门检类型: 1:回场;2:出场;3:进出场
  //     CheckTimeType: 2, // 检测时效: 1:常态化;2:临时检测
  //     CheckStartAt: new Date().getTime() / 1000,
  //     CheckEndAt: new Date().getTime() / 1000,
  //     IsDescNormal: 2, // 正常是否必传描述 1:是;2:否
  //     IsDescAbNormal: 2, // 异常是否必传描述 1:是;2:否
  //     // 异常标签
  //     AbNormalItems: null
  //   }
  // ];

  data.forEach((v, i) => {
    v = project_changeData(v);
  });

  project_list.value = data;
  project_list_base = JSON.parse(JSON.stringify(data));
};
// 检查项目-处理原始数据
const project_changeData = v => {
  v.DealAbNormalMethodStr = getDealAbNormalMethodStr(v.DealAbNormalMethod);
  v.CheckTimeTypeStr = getCheckTimeTypeStr(v.CheckTimeType);
  v.CheckTypes = v.CheckType ? (v.CheckType == 3 ? [1, 2] : [v.CheckType]) : [];
  v.CheckAt = v.CheckStartAt
    ? [v.CheckStartAt * 1000, v.CheckEndAt * 1000]
    : [];
  v.IsDescNormalBool = v.IsDescNormal == 1;
  v.IsDescAbNormalBool = v.IsDescAbNormal == 1;
  v.IsAttachmentAbNormalBool = v.IsAttachmentAbNormal == 1;
  v.CategoryId = v.CategoryId.toString();

  v.title = v.Name;
  v.seq = v.Sort;
  v.headImgUrl = v.IconFileUrl;
  v.labelList = [
    {
      show: v.CheckType == 2 || v.CheckType == 3,
      name: "出场门检",
      fontColor: "rgba(56, 185, 24, 1)",
      bgColor: "rgba(56, 185, 24, 0.102)"
    },
    {
      show: v.CheckType == 1 || v.CheckType == 3,
      name: "回场门检",
      fontColor: "rgba(102, 180, 242, 1)",
      bgColor: "rgba(102, 180, 242, 0.102)"
    },
    {
      show: v.DealAbNormalMethod == 1,
      name: "修理厂处理",
      fontColor: "rgba(141, 141, 141, 1)",
      bgColor: "rgba(141, 141, 141, 0.1)"
    },
    {
      show: v.DealAbNormalMethod == 2,
      name: "车队处理",
      fontColor: "rgba(141, 141, 141, 1)",
      bgColor: "rgba(141, 141, 141, 0.102)"
    },
    {
      show: v.CheckTimeType == 1,
      name: "常态化",
      fontColor: "rgba(102, 180, 242, 1)",
      bgColor: "rgba(102, 180, 242, 0.102)"
    },
    {
      show: v.CheckTimeType == 2,
      name: "临时检测",
      fontColor: "rgba(252, 138, 40, 1)",
      bgColor: "rgba(252, 138, 40, 0.102)"
    },
    {
      show: v.OutDepotAbNormal == 1,
      name: "允许出场",
      fontColor: "rgba(56, 185, 24, 1)",
      bgColor: "rgba(56, 185, 24, 0.102)"
    },
    {
      show: v.OutDepotAbNormal == 2,
      name: "不允许出场",
      fontColor: "rgba(240, 48, 48, 1)",
      bgColor: "rgba(240, 48, 48, 0.102)"
    }
  ];

  return v;
};
// 检查项目-折叠
const project_collapseChange = (e, item) => {
  item.collapse = e;
};
// 检查项目-新增
const project_add = () => {
  // 检测当前是否有正在编辑的检查项目
  if (project_list.value.find(v => v.openEdit)) {
    ElMessage.info("有检查项目正在编辑中");
    return;
  }

  let newData = project_changeData({
    Id: new Date().getTime(),
    // Name: "",
    CategoryId: "",
    // Sort: 0,
    // IconFileUrl: "",
    // IconFileId: 0,
    DealAbNormalMethod: 1, // 异常处理: 1:修理厂修理;2:车队处理;3:设备工单
    // IsAttachmentAbNormal: 1, // 门检项目-异常是否上传附件 1:是;2:否
    OutDepotAbNormal: 1, // 异常出场: 1:允许; 2:不允许
    // CheckType: 3, // 门检类型: 1:回场;2:出场;3:进出场
    CheckTimeType: 1, // 检测时效: 1:常态化;2:临时检测
    // CheckStartAt: new Date().getTime() / 1000,
    // CheckEndAt: new Date().getTime() / 1000,
    // IsDescNormal: 1, // 正常是否必传描述 1:是;2:否
    // IsDescAbNormal: 1, // 异常是否必传描述 1:是;2:否
    // 异常标签
    AbNormalItems: [],
    isAdd: true
  });

  abNormal_add(newData);
  project_edit(newData);

  project_list.value.unshift(newData);
};
// 检查项目-编辑
const project_edit = project => {
  // 检测当前是否有正在编辑的检查项目
  if (project_list.value.find(v => v.openEdit)) {
    ElMessage.info("有检查项目正在编辑中");
    return;
  }

  if (!project.collapse?.length) {
    project.collapse = ["1"];
  }
  project.openEdit = true;
};
// 检查项目-删除
const project_del = project => {
  if (project.isAdd) {
    // 若为新增数据，直接删除本地未提交的数据
    let dataIndex = project_list.value.findIndex(v => v.Id == project.Id);
    if (dataIndex > -1) {
      project_list.value.splice(dataIndex, 1);
    }
  } else {
    ElMessageBox.confirm(`确定删除 ${project.title} 检查项目?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info"
    }).then(async () => {
      await DelProject({
        Ids: [JSONbig.parse(project.Id)]
      });
      ElMessage.success("删除成功");

      // 删除后不刷整个检查项目列表，删除本地数据
      // 避免其它正在编辑的检查项目 被强制刷新
      let dataIndex = project_list.value.findIndex(v => v.Id == project.Id);
      if (dataIndex > -1) {
        project_list.value.splice(dataIndex, 1);
      }
    });
  }
};
// 检查项目-保存
const project_confirm = async (project, projectRef) => {
  console.log(project);
  projectRef.validate(async (valid: any) => {
    if (valid) {
      if (project.AbNormalItems.find(v => !v.Name)) {
        ElMessage.warning("异常标签内容不能为空");
        return;
      }
      if (!project.CheckTypes.length) {
        ElMessage.warning("请选择门检类型");
        return;
      }
      if (project.CheckTimeType == 2 && !project.CheckAt.length) {
        ElMessage.warning("请选择检测时段");
        return;
      }

      let abAdd = [];
      let abEdit = [];
      let abDel = [];

      if (project.isAdd) {
        abAdd = project.AbNormalItems;
      } else {
        let baseData = project_list_base.find(v => v.Id == project.Id);
        project.AbNormalItems.forEach(v => {
          if (!v.Id) {
            abAdd.push(v);
          } else {
            let abBase = baseData.AbNormalItems.find(b => b.Id == v.Id);
            if (abBase.Name != v.Name) {
              abEdit.push(v);
            }
          }
        });
        abDel = baseData.AbNormalItems.filter(v => {
          return !project.AbNormalItems.find(b => b.Id == v.Id);
        }).map(v => JSONbig.parse(v.Id));
      }

      if (projectType.value.Type == 2) {
        // 设备检查类型
        project.Name =
          dictList.value.find(v => v.Id == project.CategoryId)?.DictKey ?? "";
      }

      let req = {
        CorporationId: JSONbig.parse(queryReq.CorporationId),
        ProjectType: projectType.value.Type,
        ProjectTypeId: JSONbig.parse(projectType.value.Id),
        Name: project.Name,
        CategoryId: JSONbig.parse(project.CategoryId || 0),
        Sort: project.Sort,
        IconFileId: JSONbig.parse(project.IconFileId),
        DealAbNormalMethod: project.DealAbNormalMethod,
        IsAttachmentAbNormal: project.IsAttachmentAbNormalBool ? 1 : 2,
        OutDepotAbNormal: project.OutDepotAbNormal,
        CheckType: project.CheckTypes.length == 2 ? 3 : project.CheckTypes[0],
        CheckTimeType: project.CheckTimeType,
        CheckStartAt:
          new Date(
            new Date(project.CheckAt[0]).toLocaleDateString() + " 00:00:00"
          ).getTime() / 1000,
        CheckEndAt:
          new Date(
            new Date(project.CheckAt[1]).toLocaleDateString() + " 23:59:59"
          ).getTime() / 1000,
        IsDescNormal: project.IsDescNormalBool ? 1 : 2,
        IsDescAbNormal: project.IsDescAbNormalBool ? 1 : 2,
        AddAbNormalItems: abAdd,
        EditAbNormalItems: abEdit,
        DelAbNormalItems: abDel
      };
      console.log(req);

      if (project.isAdd) {
        await AddProject(req);
        ElMessage.success("新增成功");
        project_getList();
      } else {
        req.Id = JSONbig.parse(project.Id);
        await EditProject(req);
        ElMessage.success("编辑成功");
        project_getList();
      }
    }
  });
};
// 检查项目-取消
const project_cancel = project => {
  if (project.isAdd) {
    // 取消新增操作，直接删除本地未提交的数据
    project_del(project);
  } else {
    let baseData = project_list_base.find(v => v.Id == project.Id);
    if (baseData) {
      let copyData = JSON.parse(JSON.stringify(baseData));
      copyData.collapse = project.collapse;
      copyData.openEdit = false;
      project_list.value[
        project_list.value.findIndex(v => v.Id == project.Id)
      ] = copyData;
    }
  }
};
const project_checkTimeTypeChange = project => {
  if (project.CheckTimeType == 1) {
    project.CheckAt = [];
  }
};
// 检查项目-上传图片
const project_uploadImg = (file, project) => {
  if (file) {
    const imgUp = new FileUploadSlice(file.raw, {
      finishHandle: (t, res) => {
        if (t) {
          project.IconFileId = res.Data.FileId;
          project.IconFileUrl = res.Data.FileUrl;
        } else {
          ElMessage.info("图片上传失败");
        }
      }
    });
    imgUp.upload();
  } else {
    project.IconFileId = 0;
    project.IconFileUrl = "";
  }
};
// 检查项目-删除图片
const project_delImg = project => {
  project.IconFileUrl = "";
  project.IconFileId = 0;
};

// 异常项-新增
const abNormal_add = project => {
  project.AbNormalItems.push({
    Name: ""
  });
};
// 异常项-删除
const abNormal_del = (project, abNormal, index) => {
  project.AbNormalItems.splice(index, 1);
};
</script>
<style lang="scss" scoped>
.page-container {
  .p_body {
    padding: unset;
    display: flex;
  }
}

.projectTypeList {
  width: 200px;
  padding: 15px;
  border-right: 1px solid #e1e1e1;
  .projectTypeItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    margin-bottom: 3px;
    padding: 6px 10px;
    cursor: pointer;
    border-radius: 5px;
    span {
      display: block;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .btnBox {
      display: flex;
      .el-icon {
        margin-left: 5px;
      }
    }
  }
  .projectTypeItem.selected {
    background: #e8f2fb;
    color: #2492eb;
  }
  .addBtn {
    width: 100%;
    border-style: dashed;
    margin-top: 10px;
  }
}

.projectList {
  width: calc(100% - 200px);
  padding: 20px;
  height: 100%;
  // overflow: auto;
  > .head {
    text-align: right;
    padding-bottom: 20px;
  }
  .collapseList {
    padding: 2px;
    height: calc(100% - 50px);
    overflow: auto;
  }
  :deep(.projectItem) {
    padding: 0 20px;
    min-height: 60px;
    border-radius: 5px;
    opacity: 1;
    background: #ffffff;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
    .header {
      height: 100%;
      width: 100%;
      padding-right: 20px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .seq {
        display: block;
        width: 30px;
        font-size: 17px;
        font-weight: bold;
        margin-right: 20px;
      }
      .title {
        display: block;
        width: 150px;
        font-size: 17px;
        font-weight: bold;
        margin-right: 20px;
      }
      .imgBox {
        margin-right: 20px;
        height: 45px;
        width: 45px;
        border: 1px solid #efefef;
        .el-image {
          height: 100%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            height: auto;
            width: auto;
            max-width: 100%;
            max-height: 100%;
          }
        }
        .image-error {
          display: flex;
        }
      }
      .labelList {
        flex: 1;
        overflow: auto;
        text-align: left;
        white-space: nowrap;
        .labelItem {
          padding: 3px 7px;
          border-radius: 3px;
          margin-right: 10px;
        }
      }
      .labelList::-webkit-scrollbar {
        display: none;
      }
      > .btnBox {
        display: flex;
        align-items: center;
        margin-left: 20px;
        i + i {
          margin-left: 10px;
        }
      }
    }
    .content {
      position: relative;
      .formList {
        display: flex;
        flex-wrap: wrap;
        .el-form-item {
          margin: 0 10px 20px 10px;
          .el-form-item__label {
            margin-bottom: 0px;
          }
          .el-form-item__content {
            width: 220px;
            min-height: 40px;
            .el-select,
            .el-input-number {
              width: 100%;
              .el-input__inner {
                text-align: left;
              }
            }
          }
        }
        .abNormalBox {
          .el-form-item__content {
            display: block;
            width: auto;
            .checkBox {
              display: flex;
              align-items: center;
              min-height: 50px;
            }
          }
          .abNormalItem {
            display: flex;
            align-items: center;
            > * + * {
              margin-left: 5px;
            }
            i {
              cursor: pointer;
            }
          }
          .abNormalItem + .abNormalItem {
            margin-top: 10px;
          }
        }
        .uploadImgBox {
          height: 100%;
          width: 100%;
          display: flex;
          align-items: center;
          .elUpload {
            height: 40px;
            width: 40px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px dashed #ccc;
            .el-upload {
              height: 100%;
              width: 100%;
              position: relative;
            }
            .el-image {
              height: 100%;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                max-height: 100%;
                max-width: 100%;
                height: auto;
                width: auto;
              }
            }
            .imgDel {
              position: absolute;
              top: -4px;
              right: -4px;
              width: 16px;
              height: 16px;
              line-height: 17px;
              border-radius: 22px;
              color: #fff;
              font-size: 17px;
              text-align: center;
              display: none;
              background: red;
              transform: rotate(45deg);
              cursor: pointer;
            }
          }
          .tip {
            color: red;
            font-size: 12px;
            margin-left: 6px;
          }
          .elUpload:hover .imgDel {
            display: block;
          }
        }
      }
      .line {
        border-top: 1px solid #f1f1f1;
        margin: 10px 0 20px 0;
      }
      .editBtns {
        text-align: right;
        margin-top: 20px;
      }
      .editMask {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 10;
      }
    }

    .el-collapse-item__header {
      border-bottom: none;
    }
  }
  .projectItem.expend {
    .header .imgBox,
    .header .labelList {
      opacity: 0;
      pointer-events: none;
    }
  }
  :deep(.el-collapse-item__header) {
    height: 60px;
  }
}
</style>
<style>
.el-date-editor .el-range-input {
  height: 28px;
}
</style>
