<template>
  <div id="staff" class="page-container statTablePagePublicCss">
    <!-- 电车平台 -->
    <div class="p_treeBox">
      <div class="p_treeContent">
        <ReTree
          :type="2"
          width="250"
          v-show="mainTreeShow"
          ref="mainTree"
          @clickTreeNode="clickMainTreeNode"
        />
      </div>
      <div class="p_treeHideBtn" @click="handleHideMainTree">||</div>
    </div>
    <div class="p_contentBox">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="终端状态显示" name="state" :lazy="true">
          <div class="p_stateContainer">
            <!-- 状态显示列 -->
            <div class="p_stateColumn">
              <div class="p_stateBox">
                <el-input
                  disabled
                  v-model="statesList"
                  style="width: 400px"
                  :rows="18"
                  type="textarea"
                />
              </div>
              <el-button
                class="publicBtn"
                type="primary"
                @click="handleReadStatus"
              >
                读取状态
              </el-button>
            </div>
            <!-- 日志显示列 -->
            <div class="p_stateColumn">
              <div class="p_stateBox">
                <el-input
                  disabled
                  v-model="logsList"
                  style="width: 400px"
                  :rows="18"
                  type="textarea"
                />
              </div>
              <div class="p_logButtons">
                <el-button
                  class="publicBtn-two"
                  type="primary"
                  @click="handleReadLogs"
                >
                  读取日志
                </el-button>
                <el-button
                  class="publicBtn-three"
                  type="primary"
                  @click="handleClearLogs"
                >
                  清除日志
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="终端参数设置" name="settings" :lazy="true">
          <div class="p_settingsBox">
            <el-form class="p_settingsForm" label-width="100px" width="100%">
              <!-- 第一列 -->
              <div class="p_settingsColumn">
                <el-form-item
                  v-for="param in columnOneParams"
                  :key="param.param_id"
                  :label="param.param_name"
                  :prop="param.param_name"
                >
                  <el-input
                    v-model="dynamicSettingsForm[param.param_name]"
                    :placeholder="`请输入${param.param_name}`"
                    style="width: 300px"
                  />
                </el-form-item>
              </div>

              <!-- 第二列 -->
              <div class="p_settingsColumn">
                <el-form-item
                  v-for="param in columnTwoParams"
                  :key="param.param_id"
                  :label="param.param_name"
                  :prop="param.param_name"
                >
                  <el-input
                    v-if="param.param_name !== '平台协议标准'"
                    v-model="dynamicSettingsForm[param.param_name]"
                    :placeholder="`请输入${param.param_name}`"
                    style="width: 300px"
                  />
                  <el-select
                    v-else
                    v-model="protocolSettingsForm['平台协议标准']"
                    placeholder="请选择协议标准"
                    style="width: 300px"
                  >
                    <el-option
                      v-for="option in protocolOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 第三列 -->
              <div class="p_settingsColumn">
                <el-form-item
                  v-for="param in columnThreeParams"
                  :key="param.param_id"
                  :label="param.param_name"
                  :prop="param.param_name"
                >
                  <el-input
                    v-if="param.param_name !== '本地协议标准'"
                    v-model="dynamicSettingsForm[param.param_name]"
                    :placeholder="`请输入${param.param_name}`"
                    style="width: 300px"
                  />
                  <el-select
                    v-else
                    v-model="protocolSettingsForm['本地协议标准']"
                    placeholder="请选择协议标准"
                    style="width: 300px"
                  >
                    <el-option
                      v-for="option in protocolOptionsTwo"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <!--             <el-form-item
                v-for="param in filteredParams"
                :key="param.param_id"
                :label="param.param_name"
                :prop="param.param_name"
              >
                <el-input
                  v-model="dynamicSettingsForm[param.param_name]"
                  :placeholder="`请输入${param.param_name}`"
                  style="width: 200px"
                />
              </el-form-item>-->

              <!-- <el-form-item label="平台协议标准">
                <el-select
                  v-model="protocolSettingsForm['平台协议标准']"
                  placeholder="请选择协议标准"
                  style="width: 200px"
                >
                  <el-option
                    v-for="option in protocolOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="本地协议标准">
                <el-select
                  v-model="protocolSettingsForm['本地协议标准']"
                  placeholder="请选择协议标准"
                  style="width: 200px"
                >
                  <el-option
                    v-for="option in protocolOptionsTwo"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item> -->
            </el-form>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: flex-start;
                margin-top: 40px;
                margin-left: 108px;
                gap: 80px;
              "
            >
              <el-button
                class="publicBtn-five"
                type="success"
                @click="handleReadSettings"
                >读取
              </el-button>
              <el-button
                class="publicBtn-three"
                type="primary"
                @click="handleUpdateSettings"
                >修改
              </el-button>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="终端远程升级" name="parameter" :lazy="true">
          <div class="contentBox">
            <div class="p_stateBox">
              <el-input
                disabled
                v-model="tableData"
                style="width: 700px"
                :rows="24"
                type="textarea"
              />
              <el-button
                class="publicBtn-four"
                type="primary"
                @click="handleReadParameter"
                >读取</el-button
              >
            </div>
            <div class="uploadImgBox">
              <el-upload
                ref="uploadRef"
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleFileChange"
                :show-file-list="false"
              >
                <template #trigger>
                  <div class="ml-2">
                    <el-button type="primary">选择固件文件</el-button>
                  </div>
                </template>
                <el-button
                  class="ml-3"
                  type="success"
                  :disabled="!selectedFile"
                  @click="handleUpload"
                >
                  开始升级
                </el-button>
              </el-upload>
              <!-- 文件信息展示 -->
              <div v-if="selectedFile" class="file-info">
                <div class="file-meta">
                  <span class="file-name">{{ selectedFile.name }}</span>
                  <span class="file-size"
                    >({{ formatFileSize(selectedFile.size) }})</span
                  >
                  <el-icon class="delete-icon" @click="handleClearFile">
                    <Close />
                  </el-icon>
                </div>
                <div class="file-status">
                  <span class="ready-text">准备就绪</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="添加账号"
          name="account"
          :lazy="true"
          v-if="isRootUser"
        >
          <div class="account-container">
            <el-form
              ref="accountFormRef"
              :model="accountForm"
              :rules="accountRules"
              label-width="120px"
              class="account-form"
            >
              <el-form-item label="账号" prop="username">
                <el-input
                  v-model="accountForm.username"
                  placeholder="请输入账号"
                  style="width: 300px"
                />
              </el-form-item>

              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="accountForm.password"
                  type="password"
                  placeholder="请输入密码"
                  style="width: 300px"
                  show-password
                />
              </el-form-item>

              <el-form-item label="权限" prop="role">
                <el-select
                  v-model="accountForm.role"
                  placeholder="请选择权限"
                  style="width: 300px"
                >
                  <el-option label="admin" value="admin" />
                  <el-option label="root" value="root" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="submitAccount"
                  class="publicBtn-three"
                >
                  添加账号
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import ReTree from "@/components/ReTree/index.vue";
import {
  getDeviceStatus,
  getDeviceParams,
  updateDeviceParams,
  getDeviceAttrs,
  getDeviceLogs,
  clearDeviceLogs
  // upgradeDeviceFirmware
} from "@/api/dss/staffs";
import { AddUsers, AuthLogin } from "@/api/user";
import {
  ElMessage,
  ElUpload,
  UploadInstance,
  UploadFile,
  FormInstance,
  FormRules
} from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
// 获取用户store
const userStore = useUserStoreHook();
const isRootUser = computed(() => {
  console.log("当前用户角色:", userStore.role); // 调试用
  return userStore.role === "root";
});
defineOptions({
  name: "staff"
});

const activeName = ref("state");
const statesList = ref("");
const logsList = ref("");
const tableData = ref();
// 左侧树显隐
const mainTree = ref(null);
const mainTreeShow = ref(true);
const handleHideMainTree = () => {
  mainTreeShow.value = !mainTreeShow.value;
};
const selectNode = reactive({ phone: "", ip: "", lastHeartbeat: null });
// 是否显示平台协议标准
const showPlatformProtocol = ref(false);

// 是否显示本地协议标准
const showLocalProtocol = ref(false);

// 动态参数列表和表单数据
const dynamicParams = ref<
  { param_name: string; param_id: number; parsed_value: string | number }[]
>([]);

//普通参数表单
const dynamicSettingsForm = reactive<Record<string, string | number>>({});
// 协议参数表单
const protocolSettingsForm = reactive<Record<string, number>>({
  // 平台协议标准: 1, // 默认值
  // 本地协议标准: 1 // 默认值
});

// 过滤后的普通参数列表
const filteredParams = computed(() => {
  return dynamicParams.value.filter(
    param =>
      param.param_name !== "平台协议标准" && param.param_name !== "本地协议标准"
  );
});

// 平台选项配置
const protocolOptions = ref([
  { value: 0, label: "内部测试" },
  { value: 1, label: "星象智联" }
]);
// 本地选项配置
const protocolOptionsTwo = ref([
  { value: 0, label: "内部测试" },
  { value: 1, label: "晶汇博来" }
]);

// 终端参数设置表单
const settingsForm = reactive({
  phone: "",
  productId: "",
  deviceType: "",
  platformProtocol: "",
  controlProtocol: "",
  platformIp: "",
  platformPort: ""
});

const accountFormRef = ref<FormInstance>(); // 账号表单引用
const accountForm = reactive({
  username: "",
  password: "",
  role: ""
});

// 添加账号校验
const accountRules = reactive<FormRules>({
  username: [
    { required: true, message: "请输入账号", trigger: "blur" },
    { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  role: [{ required: true, message: "请选择权限", trigger: "change" }]
});

const clickMainTreeNode = (node: any) => {
  Object.assign(selectNode, node); // 保存选中的设备信息
  settingsForm.phone = node.phone || ""; // 同步设备编号到表单
  // settingsForm.platformIp = node.ip; // 同步IP到表单
  // if (activeName.value === "state") {
  //   handleReadStatus(); // 自动读取状态
  // } else if (activeName.value === "settings") {
  //   handleReadSettings(); // 自动读取参数
  // }
};

// Base64 解码函数
const decodeBase64 = (base64Str: string): any => {
  const decoded = atob(base64Str);
  return JSON.parse(decoded);
};

// 读取设备状态
const handleReadStatus = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }
  const resp = await getDeviceStatus(selectNode.phone);
  if (!resp?.data?.status) {
    statesList.value = "无状态信息";
    ElMessage.warning("未获取到设备状态");
    return;
  }

  // 解码 Base64 字符串
  // const decodedData = decodeBase64(resp.data.status);
  // if (!decodedData || !decodedData.data?.status) {
  //   statesList.value = "状态数据解析失败";
  //   ElMessage.error("状态数据解析失败");
  //   return;
  // }
  //
  // // 提取状态对象并格式化为键值对
  // const statusObj = decodedData.data.status;
  // const lines = Object.entries(statusObj).map(
  //   ([key, value]) => `${key}: ${value}`
  // );
  statesList.value = resp.data.status;

  ElMessage.success("状态读取成功");
};

//读取设备日志
const handleReadLogs = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }
  const resp = await getDeviceLogs(selectNode.phone);
  logsList.value = resp.data.log;
  ElMessage.success("日志读取成功");
};

//清除设备日志
const handleClearLogs = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }
  await clearDeviceLogs(selectNode.phone);
  logsList.value = "";
  ElMessage.success("日志已清除");
};

// 读取设备参数
const handleReadSettings = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }
  const resp = await getDeviceParams(selectNode.phone);
  if (!resp?.data?.params?.length) {
    ElMessage.warning("未获取到设备参数");
    dynamicParams.value = [];
    showPlatformProtocol.value = false; // 没有参数时不显示平台协议标准
    showLocalProtocol.value = false; // 没有参数时不显示本地协议标准
    return;
  }

  // 更新动态参数列表
  dynamicParams.value = resp.data.params;

  // 清空表单数据
  Object.keys(dynamicSettingsForm).forEach(
    key => delete dynamicSettingsForm[key]
  );
  Object.keys(protocolSettingsForm).forEach(
    key => delete protocolSettingsForm[key]
  );

  // 检查是否存在平台协议标准和本地协议标准
  showPlatformProtocol.value = dynamicParams.value.some(
    param => param.param_name === "平台协议标准"
  );
  showLocalProtocol.value = dynamicParams.value.some(
    param => param.param_name === "本地协议标准"
  );

  // 更新普通参数表单数据
  dynamicParams.value.forEach(param => {
    if (
      param.param_name !== "平台协议标准" &&
      param.param_name !== "本地协议标准"
    ) {
      dynamicSettingsForm[param.param_name] = param.parsed_value;
    }
  });

  // 处理平台协议标准和本地协议标准
  const platformProtocolParam = dynamicParams.value.find(
    param => param.param_name === "平台协议标准"
  );
  const localProtocolParam = dynamicParams.value.find(
    param => param.param_name === "本地协议标准"
  );

  // 设置平台协议标准的值
  if (platformProtocolParam) {
    const platformValue = Number(platformProtocolParam.parsed_value);
    // 修正类型错误，确保赋值为有效的数字
    protocolSettingsForm["平台协议标准"] = platformValue;
    platformValue === 1 ? platformValue : null;
  }

  // 设置本地协议标准的值
  if (localProtocolParam) {
    const localValue = Number(localProtocolParam.parsed_value);
    protocolSettingsForm["本地协议标准"] = localValue;
    localValue === 1 ? localValue : null;
  }

  ElMessage.success("参数读取成功");
};

// 根据参数 ID 分组
const columnOneParams = computed(() => {
  return filteredParams.value
    .filter(param => String(param.param_id).startsWith("4"))
    .slice(0, 4); // 最多显示 4 个
});

const columnTwoParams = computed(() => {
  const params = [];

  // 如果有平台协议标准，添加到第二列第一个
  if (showPlatformProtocol.value) {
    params.push({
      param_id: 9998,
      param_name: "平台协议标准",
      parsed_value: protocolSettingsForm["平台协议标准"] || 1
    });
  }

  params.push(
    ...filteredParams.value
      .filter(param => String(param.param_id).startsWith("8"))
      .slice(0, 3) // 最多显示 3 个普通参数
  );

  return params;
});

const columnThreeParams = computed(() => {
  const params = [];

  // 如果有本地协议标准，添加到第三列第一个
  if (showLocalProtocol.value) {
    params.push({
      param_id: 9999, // 特殊ID，避免冲突
      param_name: "本地协议标准",
      parsed_value: protocolSettingsForm["本地协议标准"] || 1
    });
  }

  params.push(
    ...filteredParams.value
      .filter(param => String(param.param_id).startsWith("12"))
      .slice(0, 3) // 最多显示 3 个普通参数
  );

  return params;
});

// 修改设备参数
const handleUpdateSettings = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }

  const updateParams = [
    ...dynamicParams.value
      .filter(
        param =>
          param.param_name !== "平台协议标准" &&
          param.param_name !== "本地协议标准"
      )
      .map(param => ({
        id: param.param_id,
        value: dynamicSettingsForm[param.param_name]?.toString() || ""
      })),

    // 平台协议标准
    ...(showPlatformProtocol.value
      ? [
          {
            id:
              dynamicParams.value.find(
                param => param.param_name === "平台协议标准"
              )?.param_id || 9998,
            value: protocolSettingsForm["平台协议标准"]?.toString() || "1"
          }
        ]
      : []),

    // 本地协议标准
    ...(showLocalProtocol.value
      ? [
          {
            id:
              dynamicParams.value.find(
                param => param.param_name === "本地协议标准"
              )?.param_id || 9999,
            value: protocolSettingsForm["本地协议标准"]?.toString() || "1"
          }
        ]
      : [])
  ];

  await updateDeviceParams(selectNode.phone, updateParams);
  ElMessage.success("参数修改成功");
};

const handleReadParameter = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择左侧设备");
    return;
  }

  // 调用查询终端属性接口
  const resp = await getDeviceAttrs(selectNode.phone);

  // 提取终端属性数据
  const attrs = resp?.data;

  // 检查响应数据是否有效
  if (!attrs) {
    ElMessage.warning("未获取到终端属性");
    tableData.value = "无属性信息";
    return;
  }

  // 将属性数据格式化为键值对
  const lines = [
    `终端类型: ${attrs.terminal_type}`,
    `制造商ID: ${attrs.manufacturer_id}`,
    `终端型号: ${attrs.terminal_model}`,
    `终端ID: ${attrs.terminal_id}`,
    `SIM卡ICCID: ${attrs.iccid}`,
    `硬件版本号: ${attrs.hardware_version}`,
    `固件版本号: ${attrs.firmware_version}`,
    `GNSS模块属性: ${attrs.gnss_module}`,
    `通信模块属性: ${attrs.comm_module}`
  ];

  // 更新页面显示
  tableData.value = lines.join("\n");

  ElMessage.success("终端属性读取成功");
};

const uploadRef = ref<UploadInstance>();
const selectedFile = ref<File | null>(null); // 选择的文件
const uploadLoading = ref(false); // 上传加载状态

// 处理文件选择
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) {
    ElMessage.warning("文件选择失败");
    return;
  }

  // 文件大小限制(最大100MB)
  const maxSize = 1024 * 1024 * 1024;
  if (file.raw.size > maxSize) {
    ElMessage.error("文件大小不能超过1GB");
    return;
  }

  selectedFile.value = file.raw;
};

// 文件大小格式化
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 清除已选文件
const handleClearFile = () => {
  selectedFile.value = null;
  uploadRef.value?.clearFiles();
};

// 升级确认处理
const handleUpload = async () => {
  if (!selectNode.phone) {
    ElMessage.warning("请先选择设备");
    return;
  }
  if (!selectedFile.value) {
    ElMessage.warning("请先选择文件");
    return;
  }

  uploadLoading.value = true;
  // 创建 FormData 对象
  const formData = new FormData();
  formData.append("firmware", selectedFile.value);
  // await upgradeDeviceFirmware(selectNode.phone, selectedFile.value);
  const response = await fetch(
    `${import.meta.env.VITE_APP_BASE_API_PROXY}/api/devices/${selectNode.phone}/upgrade`,
    {
      method: "POST",
      body: formData
    }
  );

  if (response.ok) {
    ElMessage.success("固件升级成功");
    handleClearFile(); // 清空已选文件
  } else {
    ElMessage.error("固件升级失败");
  }
  handleClearFile(); // 清空已选文件
};

//提交账号表单
const submitAccount = async () => {
  if (!accountFormRef.value) return;
  await accountFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const userData = {
        username: accountForm.username,
        password: accountForm.password,
        role: accountForm.role
      };

      await AddUsers(userData);

      ElMessage.success("账号添加成功");
      accountFormRef.value.resetFields();
    }
  });
};

// 选项卡切换
const handleClick = (tab: any) => {
  if (tab.props.name === "state" && selectNode.phone) {
    handleReadStatus(); // 切换到状态页时自动读取
  } else if (tab.props.name === "settings" && selectNode.phone) {
    handleReadSettings(); // 切换到设置页时自动读取
  } else if (tab.props.name === "parameter" && selectNode.phone) {
    handleReadParameter(); // 切换到参数页时自动读取
  }
};
</script>
<style lang="scss">
#staff {
  .el-tabs__nav-scroll {
    background: white;
    padding: 14px 0px 0px 20px;
  }
  .p_stateContainer {
    display: flex;
    gap: 40px;
    padding: 40px 0 0 80px;
    .p_stateColumn {
      display: flex;
      flex-direction: column;
      .p_stateBox {
        padding: 40px 0px 0px 80px;
      }
      .p_logButtons {
        display: flex;
        justify-content: center;
      }
    }
  }
  .p_stateBox {
    padding: 40px 0px 0px 80px;
  }

  .publicBtn {
    width: 120px;
    height: 40px;
    margin-left: 220px;
    margin-top: 50px;
  }
  .publicBtn-two {
    width: 120px;
    height: 40px;
    margin-left: 90px;
    margin-top: 50px;
  }
  .publicBtn-three {
    width: 120px;
    height: 40px;
    margin-top: 50px;
    margin-left: 50px;
  }
  .publicBtn-four {
    width: 120px;
    height: 40px;
    margin-left: 280px;
    margin-top: 50px;
  }
  .publicBtn-five {
    width: 120px;
    height: 40px;
    margin-left: 300px;
    margin-top: 50px;
  }

  .el-tabs__nav-wrap {
    height: 54px;
  }

  .account-container {
    padding: 40px 0 0 80px;
  }

  .account-form {
    max-width: 470px;
    padding: 0px 0px 0px 80px;
    .el-form-item__content {
      display: flex;
      flex-direction: column;
    }

    .el-button {
      margin-top: 20px;
    }
  }
}
</style>
<style lang="scss" scoped>
.sendDialog .content {
  height: 40vh !important;
  display: flex;
  flex-direction: column;
  height: 100%;
  .tableBox {
    flex: 1;
    overflow: auto;
    .el-table {
      height: 100%;
    }
  }
}

.publicBtnFour {
  width: 180px;
  height: 60px;
  margin-left: 200px;
  margin-top: 200px;
}
.p_settingsBox {
  width: 800px;
  padding: 80px 0px 0px 0px;
  .p_settingsForm {
    // align-items: center;
    // justify-content: space-between;
    // flex-wrap: wrap;
    gap: 40px; // 列间距
    display: flex;
    .p_settingsColumn {
      flex: 1;
      max-width: 300px;
    }
  }
}
.editDialog {
  .info {
    display: flex;
    .avatarBox {
      width: 150px;
      height: 180px;
      background: #efefef;
      border: 1px dashed #ccc;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .el-input {
      width: 170px;
    }
  }
  .el-form-item {
    flex: 1;
  }
  .el-form-item + .el-form-item {
    margin-left: 15px;
  }
  .el-select,
  .el-input {
    width: 100%;
  }
}

.uploadImgBox {
  height: 100%;
  width: 100%;
  justify-content: center;
  margin-top: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .elUpload {
    height: 40px;
    width: 40px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #ccc;
    .el-upload {
      height: 100%;
      width: 100%;
      position: relative;
    }
    .el-image {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        max-height: 100%;
        max-width: 100%;
        height: auto;
        width: auto;
      }
    }
    .imgDel {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 16px;
      height: 16px;
      line-height: 17px;
      border-radius: 22px;
      color: #fff;
      font-size: 17px;
      text-align: center;
      display: none;
      background: red;
      transform: rotate(45deg);
      cursor: pointer;
    }
  }
  .tip {
    color: red;
    font-size: 12px;
    margin-left: 6px;
  }
  .elUpload:hover .imgDel {
    display: block;
  }
}

.file-info {
  .file-meta-detail {
    color: #909399;
    font-size: 0.9em;
  }

  .warning-text {
    color: #e6a23c;
  }

  .loading-icon {
    animation: rotating 2s linear infinite;
    margin-right: 5px;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.publicBtnFour {
  position: relative;

  &:hover::after {
    content: "请确保文件来源可靠";
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #fef0f0;
    color: #f56c6c;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
  }
}

.upload-demo {
  display: flex;
  align-items: center;
}

.file-info {
  margin-top: 20px;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-name {
  font-weight: bold;
}

.file-size {
  color: #909399;
}

.delete-icon {
  cursor: pointer;
  color: #f56c6c;
}

.file-status {
  margin-top: 5px;
}

.uploading-text {
  color: #e6a23c;
}

.ready-text {
  color: #67c23a;
}

.contentBox {
  display: flex;
  .publicBtn-two {
    width: 120px;
    height: 40px;
    margin-left: 140px;
    margin-top: 50px;
  }
}
</style>
