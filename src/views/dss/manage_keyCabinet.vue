<template>
  <div id="deviceLogs" class="page-container statTablePagePublicCss">
    <!-- 钥匙柜管理 -->
    <div class="p_contentBox">
      <div class="p_head">
        <el-form
          :model="queryForm"
          ref="queryFormRef"
          :inline="true"
          @keyup.enter="handleQuery"
          class="p_queryForm"
        >
          <el-tree-select
            v-model="queryForm.CorporationIdStr"
            :data="corpTree"
            style="width: 180px; margin-right: 20px"
            :props="corpTreeProp"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            show-checkbox
            filterable
            ref="queryCropTree"
            value-key="IdStr"
            node-key="IdStr"
            :default-expanded-keys="selectCorpIds"
          />
          <el-select
            v-model="queryForm.ParkingIds"
            filterable
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择场站名称"
            class="select-form"
          >
            <el-option
              v-for="item in parkList"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id"
            />
          </el-select>
          <el-input
            v-model="queryForm.Code"
            placeholder="请输入钥匙柜编号"
            @keyup.enter="handleQuery"
            class="select-form"
          />
          <el-input
            @keyup.enter="handleQuery"
            v-model="queryForm.License"
            placeholder="请输入车牌号"
            clearable
            class="select-form"
          />
          <el-input
            v-model.number="queryForm.BoxNum"
            placeholder="请输入钥匙柜格数"
            @keyup.enter="handleQuery"
            class="select-form"
          />
          <el-select
            v-model="queryForm.IsOnline"
            placeholder="在线状态"
            clearable
            class="select-form"
          >
            <el-option
              v-for="item in PresenceList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="queryForm.UseStatus"
            placeholder="使用状态"
            clearable
            class="select-form"
          >
            <el-option
              v-for="item in activeStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div class="form-group">
            <el-input
              v-model="queryForm.NotUseBoxMin"
              placeholder="最小空闲"
              class="select-formTwo"
            />
            <p style="padding: 0px 5px 0px 5px">~</p>
            <el-input
              v-model="queryForm.NotUseBoxMax"
              placeholder="最大空闲"
              class="select-form"
            />
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </div>
        </el-form>
        <div class="query-button">
          <el-button
            v-permission="'/ipoc/keycabinet/OpenLog'"
            @click="keycabinetLogs"
          >
            钥匙柜日志
          </el-button>
          <el-button
            type="primary"
            @click="addKeyCabinet"
            v-permission="'/ipoc/keycabinet/Create'"
          >
            新增钥匙柜
          </el-button>
        </div>
      </div>
      <div class="p_body">
        <el-table
          style="width: 100%"
          highlight-current-row
          show-overflow-tooltip
          border
          :data="tableList"
          virtual-scroll
          scrollbar-always-on
        >
          <el-table-column
            prop="Seq"
            label="序号"
            width="60"
            fixed="left"
            align="center"
          />
          <el-table-column
            prop="Code"
            label="钥匙柜编号"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="Name"
            label="钥匙柜名称"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="CorporationName"
            label="关联机构"
            min-width="120"
            align="center"
            value-key="IdStr"
            node-key="IdStr"
          />
          <el-table-column
            prop="ParkingName"
            label="关联场站"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="BoxNum"
            label="格数"
            min-width="80"
            align="center"
          >
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEdit(row, 'look')">
                {{ row.BoxNum }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="NotUseBoxNum"
            label="空闲格数"
            min-width="80"
            align="center"
          />
          <el-table-column
            prop="IsOnlineStr"
            label="在线状态"
            min-width="80"
            align="center"
          />
          <el-table-column
            prop="UseStatusStr"
            label="使用状态"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="QrcodePath"
            label="二维码"
            min-width="60px"
            align="center"
          >
            <template #default="{ row }">
              <el-image
                style="width: 50px; height: 50px"
                :src="row.QrcodePath"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[row.QrcodePath]"
                :initial-index="4"
                fit="cover"
                preview-teleported
              />
            </template>
          </el-table-column>
          <!-- 操作栏 -->
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            min-width="150"
          >
            <template #default="{ row }">
              <el-button @click="handleFullyOpen(row)" type="primary" link>
                一键全开
              </el-button>
              <el-button @click="handleEdit(row, 'edit')" type="primary" link>
                编辑
              </el-button>
              <el-button @click="handleImport(row)" link> 导入车辆 </el-button>
              <el-button @click="handleDel(row)" type="danger" link>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <RePagination
          :total="pageParamOne.total"
          v-model:offset="pageParamOne.offset"
          v-model:limit="pageParamOne.limit"
          @pagination="handleChangePageOne"
        />
      </div>
      <!-- 一键全开弹窗 -->
      <el-dialog
        title="一键开门"
        v-model="fullyOpenDialog"
        width="20%"
        class="addEditDialog dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="publicDialog">
          <span class="publicDialog-two"
            >确认要打开钥匙柜【钥匙柜编号】的所有门?
          </span>

          <span class="publicDialog-three">
            <el-button
              @click="cancelFull"
              class="publicDialog-four"
              v-loading="openLoading"
              :disabled="openLoading"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              @click="defineFull"
              class="publicDialog-five"
              v-loading="openLoading"
              :disabled="openLoading"
            >
              确定
            </el-button>
          </span>
        </div>
      </el-dialog>
      <!-- 编辑弹窗 -->
      <el-dialog
        :title="editLookTitle"
        v-model="editKeyCabinet"
        width="65%"
        class="addEditDialog dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="edit-body">
          <el-form
            label-position="top"
            ref="editFormRef"
            label-width="120px"
            :model="editFormData"
            :disabled="operateType === 'look'"
          >
            <el-form-item label="钥匙柜编号">
              <el-input v-model="editFormData.Code" disabled />
            </el-form-item>
            <el-form-item label="钥匙柜名称">
              <el-input v-model="editFormData.Name" disabled />
            </el-form-item>
            <el-form-item label="关联场站">
              <el-select v-model="editFormData.ParkingId" placeholder="请选择">
                <el-option
                  v-for="item in parkList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关联机构">
              <el-tree-select
                v-model="editFormData.CorporationId"
                :data="corpTree"
                style="width: 220px"
                :props="corpTreeProp"
                :default-expanded-keys="selectCorpIds"
                ref="addCorpTreeRef"
                filterable
              />
            </el-form-item>
            <el-form-item label="钥匙柜格数">
              <!-- required -->
              <el-input disabled v-model.number="editFormData.BoxNum" />
            </el-form-item>
          </el-form>
          <div class="edit-body-one">
            <!-- 车辆表格(编辑) -->
            <div class="bind-vehicle" v-if="operateType === 'edit'">
              <el-button type="primary" link @click="bindVehicle(true)">
                批量绑定车辆
              </el-button>
            </div>

            <el-table
              :data="editTableData"
              style="width: 100%"
              show-overflow-tooltip
              border
              scrollbar-always-on
              ref="editTableRef"
            >
              <template v-if="operateType !== 'look'">
                <el-table-column
                  v-for="(column, index) in columnsTwo"
                  :key="index"
                  :prop="column.prop"
                  :label="column.label"
                  :min-width="column.minWidth"
                  align="center"
                >
                  <template
                    #header="{ column }"
                    v-if="column.prop.startsWith('selected')"
                  >
                    <el-checkbox
                      align="center"
                      style="width: 18px"
                      v-model="headerCheckStates[index]"
                      @change="handleHeaderCheckboxChange(index)"
                    />
                  </template>
                  <template #default="{ row, $index }">
                    <!-- 勾选框列 -->
                    <el-checkbox
                      align="center"
                      style="width: 18px"
                      v-if="column.prop.startsWith('selected')"
                      v-model="row[column.prop]"
                      @change="handleCheckboxChange(row, column.prop)"
                    />
                    <span v-if="column.prop.includes('BoxNo_')">
                      {{ row[column.prop] }}
                    </span>
                    <el-button
                      type="primary"
                      link
                      v-if="
                        (column.prop.includes('License_') ||
                          column.prop.includes('License_') === null) &&
                        row['BoxNo_' + column.prop.split('_')[1]]
                      "
                      @click="bindVehicle(false, row, column.prop, $index)"
                    >
                      {{ row[column.prop] || "绑定车辆" }}
                    </el-button>
                  </template>
                </el-table-column>
              </template>
              <template v-else>
                <el-table-column
                  v-for="(column, index) in columnsOne"
                  :key="index"
                  :prop="column.prop"
                  :label="column.label"
                  :min-width="column.minWidth"
                  align="center"
                >
                  <template #default="{ row, $index }">
                    <span v-if="column.prop.includes('BoxNo_')">
                      {{ row[column.prop] }}
                    </span>
                    <el-button
                      type="primary"
                      link
                      v-if="
                        column.prop.includes('License_') &&
                        row['BoxNo_' + column.prop.split('_')[1]]
                      "
                      @click="openBoxDoor(row, column.prop)"
                    >
                      {{ row[column.prop] || "" }}
                    </el-button>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </div>
        </div>
        <template #footer v-if="operateType === 'edit'">
          <el-button type="danger" @click="handleClearEdit" style="width: 90px">
            清空绑定
          </el-button>
          <el-button
            type="primary"
            @click="handleUnbindEdit"
            style="width: 90px"
            plain
          >
            批量解绑
          </el-button>
          <el-button
            type="primary"
            @click="handleDefineEdit"
            style="width: 90px"
          >
            确定
          </el-button>
          <el-button @click="handleCancelEdit" style="width: 90px">
            取消
          </el-button>
        </template>
      </el-dialog>
      <!-- 清空绑定弹窗 -->
      <el-dialog
        title="提示"
        v-model="clearDialog"
        width="20%"
        class="addEditDialog dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="publicDialog">
          <div class="publicDialog-one">
            <el-icon color="red" size="25px"><Warning /></el-icon>
            <span class="publicDialog-two">确认要清空所有绑定车辆?</span>
          </div>
          <span class="publicDialog-three">
            <el-button @click="cancelClear" class="publicDialog-four"
              >取消</el-button
            >
            <el-button
              type="danger"
              :disabled="countdown !== 0 || !canClear"
              @click="defineClear"
              class="publicDialog-five"
            >
              清空<span v-if="countdown !== 0">({{ countdown }})</span>
            </el-button>
          </span>
        </div>
      </el-dialog>
      <!-- 批量解绑弹窗 -->
      <el-dialog
        title="解绑"
        v-model="unbindDialog"
        width="20%"
        class="addEditDialog dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="publicDialog">
          <span class="publicDialog-two">
            确认要批量解绑{{ checkTableData?.length || 0 }}辆车?
          </span>
          <span class="publicDialog-three">
            <el-button @click="cancelUnbind" class="publicDialog-four">
              取消
            </el-button>
            <el-button
              type="primary"
              :disabled="countdown !== 0 || !canClear"
              @click="defineUnbind"
              class="publicDialog-five"
            >
              确定<span v-if="countdown !== 0">({{ countdown }})</span>
            </el-button>
          </span>
        </div>
      </el-dialog>
      <!-- 钥匙柜日志弹窗 -->
      <el-dialog
        title="钥匙柜日志"
        v-model="keyCabinetLog"
        width="60%"
        class="dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="p_head">
          <el-form
            :model="queryLogForm"
            ref="queryLogFormRef"
            :inline="true"
            @keyup.enter="handleSearch"
            class="p_queryForm"
          >
            <el-date-picker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              v-model="queryLogForm.timeRange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              type="daterange"
              class="select-formFour"
            />
            <el-input
              v-model="queryLogForm.OpUser"
              placeholder="请输入操作人"
              class="select-formThree"
            />
            <el-input
              v-model.number="queryLogForm.Code"
              placeholder="请输入钥匙柜编号"
              class="select-formThree"
            />
            <el-select
              v-model="queryLogForm.ParkingIds"
              placeholder="请选择场站"
              clearable
              class="select-formThree"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
            >
              <el-option
                v-for="item in parkList"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
            <el-button type="primary" @click="handleSearch" style="width: 90px">
              搜索
            </el-button>
          </el-form>
          <el-button type="primary" @click="handleExport"> 导出 </el-button>
        </div>
        <!-- 钥匙柜日志表格 -->
        <!-- <div class="p_body" style="height: 500px"> -->
        <el-table
          style="width: 100%; height: 40vh"
          highlight-current-row
          show-overflow-tooltip
          ref="xTable"
          border
          :data="logTableData"
          virtual-scroll
          scrollbar-always-on
        >
          <el-table-column
            prop="OpUserName"
            label="操作人"
            width="90"
            fixed="left"
            align="center"
          />
          <el-table-column
            prop="WorkPostTypeStr"
            label="岗位"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="CreatedAt"
            label="操作时间"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="Code"
            label="钥匙柜编号"
            min-width="120"
            align="center"
          />
          <el-table-column
            prop="ParkingName"
            label="所属场站"
            min-width="90"
            align="center"
          />
          <el-table-column
            prop="BoxDetailStr"
            label="操作"
            min-width="150"
            align="center"
          />
          <el-table-column
            prop="FromAgentStr"
            label="控制端"
            min-width="90"
            align="center"
          />
        </el-table>
        <RePagination
          :total="pageParamTwo.total"
          v-model:offset="pageParamTwo.offset"
          v-model:limit="pageParamTwo.limit"
          @pagination="handleChangePageTwo"
        />
        <!-- </div> -->
        <template #footer>
          <el-button type="primary" @click="handleLogClose" class="log-del">
            关闭
          </el-button>
        </template>
      </el-dialog>
      <!-- 钥匙柜编号选择错误弹窗 -->
      <el-dialog
        title="提示"
        v-model="showErrorDialog"
        width="30%"
        class="addEditDialog dialogPublicCss"
        :modal-append-to-body="true"
      >
        <div class="publicDialog">
          <div class="publicDialog-one">
            <el-icon color="red" size="25px"><Warning /></el-icon>
            <span class="publicDialog-two">钥匙柜编号错误，请确认</span>
          </div>
          <span class="publicDialog-six">
            <el-button @click="closeErrorDialog">关闭</el-button>
          </span>
        </div>
      </el-dialog>
      <!-- 新增钥匙柜弹窗第一步 -->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        title="新增钥匙柜"
        v-model="addKey"
        width="600px"
        height="600px"
        class="addEditDialog dialogPublicCss"
      >
        <el-steps
          :active="active"
          class="steps-one"
          align-center
          process-status="finish"
        >
          <el-step title="步骤 1" description="录入基础信息" />
          <el-step title="步骤 2" description="导入车辆" />
        </el-steps>
        <el-form
          label-position="top"
          ref="keyCabinetFormRef"
          label-width="120px"
          :model="formModel"
          inline
          :rules="formRules"
        >
          <div class="add-KeyCabinetForm">
            <!-- required -->
            <el-form-item label="钥匙柜编号" prop="Code">
              <el-input
                placeholder="请输入"
                v-model="formModel.Code"
                @blur="validateCabinetCode"
              />
            </el-form-item>
            <el-form-item label="钥匙柜名称" prop="Name">
              <el-input placeholder="请输入" v-model="formModel.Name" />
            </el-form-item>
            <el-form-item label="关联场站" prop="ParkingId">
              <el-select
                filterable
                v-model="formModel.ParkingId"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in parkList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关联机构" prop="CorporationId">
              <el-tree-select
                v-model="formModel.CorporationId"
                :data="corpTree"
                style="width: 220px"
                :props="corpTreeProp"
                :default-expanded-keys="selectCorpIds"
                ref="addCorpTreeRef"
                filterable
              />
            </el-form-item>
            <!-- required -->
            <el-form-item label="钥匙柜格数" prop="BoxNum">
              <el-input
                placeholder="1 ~ 99"
                v-model.number.trim="formModel.BoxNum"
                :disabled="formModel.check"
              />
            </el-form-item>
          </div>
          <el-checkbox
            label="自动获取"
            v-model="formModel.check"
            class="add-KeyCabinetFormTwo"
            @change="checkChange"
          />
        </el-form>
        <div class="next-class">
          <el-button @click="nextStep" type="primary">下一步</el-button>
          <el-button @click="nextCancel" style="width: 80px">取消</el-button>
        </div>
      </el-dialog>

      <!-- 新增钥匙柜弹窗第二步 -->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        title="新增钥匙柜"
        v-model="addKeyTwo"
        width="1000px"
        class="addEditDialog dialogPublicCss"
        style="margin-top: 20px"
      >
        <el-steps
          class="steps-two"
          :active="active"
          align-center
          process-status="finish"
        >
          <el-step title="步骤 1" description="录入基础信息" status="wait" />
          <el-step title="步骤 2" description="导入车辆" status="finish" />
        </el-steps>
        <div class="bind-vehicle">
          <el-button type="primary" link @click="clearBindVehicle">
            批量清空车辆
          </el-button>
          <el-button type="primary" link @click="bindVehicle(true)">
            批量绑定车辆
          </el-button>
        </div>
        <!-- 车辆表格 -->
        <el-table
          :data="addTableData"
          style="width: 100%"
          show-overflow-tooltip
          border
          scrollbar-always-on
          ref="addTableRef"
        >
          <el-table-column
            v-for="(column, index) in columnsOne"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            :min-width="column.minWidth"
            align="center"
          >
            <template #default="{ row, $index }">
              <span v-if="column.prop.includes('BoxNo_')">
                {{ row[column.prop] }}
              </span>
              <el-button
                type="primary"
                link
                v-if="
                  column.prop.includes('License_') &&
                  row['BoxNo_' + column.prop.split('_')[1]]
                "
                @click="bindVehicle(false, row, column.prop, $index)"
              >
                {{ row[column.prop] || "绑定车辆" }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <el-button @click="previousStep">上一步</el-button>
          <el-button @click="submitForm" type="primary">完成</el-button>
          <el-button @click="nextCancelTwo">取消</el-button>
        </template>
      </el-dialog>
      <!-- 绑定车辆 -->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        title="选择车辆"
        v-model="bindVehicleDialog"
        width="360px"
        class="addEditDialog dialogPublicCss"
        @close="addBindVehicleCancel"
      >
        <div class="tree-container" v-loading="loading">
          <el-input
            v-model="filterLicense"
            style="width: 320px"
            placeholder="请输入车牌"
          />
          <div class="treeBox">
            <el-tree
              style="max-width: 600px"
              :data="vehicleTreeData"
              :show-checkbox="isMoreBind"
              highlight-current
              :expand-on-click-node="false"
              :props="corpTreeProp"
              ref="bindTreeRef"
              value-key="IdStr"
              node-key="IdStr"
              @check-change="bingTreeCheckChange"
              @node-click="bindSingleVehicle"
              :filter-node-method="filterLicenseNode"
              :current-node-key="bindVehicleId"
              :default-checked-keys="defaultCheckedKeys"
              :key="bindTreeKey"
            />
          </div>

          <span v-if="isMoreBind">
            已选：{{ checkTreeNodeNum }}/{{ allKeyNum }}
          </span>
        </div>
        <template #footer>
          <el-button @click="defineBindTree" type="primary"> 确定 </el-button>
          <el-button @click="bindVehicleCancel"> 取消 </el-button>
        </template>
      </el-dialog>

      <!-- 导入弹窗 -->
      <el-dialog
        title="导入"
        v-model="importDialog_show"
        class="importDialog dialogPublicCss"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="importDialog_cancel"
        width="400px"
      >
        <div class="uploadBox">
          <el-upload
            class="elUpload"
            drag
            action="#"
            :auto-upload="true"
            :http-request="importDialog_upload"
            :multiple="false"
            :show-file-list="false"
          >
            <el-icon style="font-size: 67px; color: #c0c4cc">
              <UploadFilled />
            </el-icon>
            <div
              v-show="importDialog_fileName"
              class="el-upload__text"
              v-html="importDialog_fileName"
            />
            <div
              v-show="!importDialog_fileName"
              class="el-upload__text"
              v-html="'将文件拖到此处，或<em>点击上传</em>'"
            />
            <template #tip>
              <div class="el-upload__tip">支持xlsx文件</div>
            </template>
          </el-upload>
          <div class="btns" style="width: 100%">
            <div>
              <el-button type="primary" @click="handleImport_download">
                导入模板下载
              </el-button>
            </div>
            <div>
              <el-button type="primary" @click="handleImport_confirm"
                >确定</el-button
              >
              <el-button @click="importDialog_show = false">取消</el-button>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "manage_keyCabinet"
});
import {
  getKeyCabinetList,
  listExportKeyCabinetList,
  createKeyCabinet,
  updateKeyCabinet,
  ShowKeyCabinetDetail,
  deleteKeyCabinet,
  checkStatusKeyCabinet,
  openBoxKeyCabinet,
  type keyCabinetListItems,
  openLogCabinet,
  ImportLicense
} from "@/api/dss/keyCabinet";
import { reactive, ref, computed, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { cloneDeep } from "@pureadmin/utils";
import { getParkingList } from "@/api/access/parking";
import { getTreeList } from "@/api/center";
import { buildCorpTree, buildVehicleTree } from "@/utils/tree";
import RePagination from "@/components/RePagination/index.vue";
import JSONbig from "json-bigint";
import { timeFormat } from "@/utils/time";
import { workPostTypeToStr } from "@/utils/code";
import { exportXlsx } from "@/utils/xlsx";

//------------------弹窗开关---------------------
const keyCabinetLog = ref(false); // 钥匙柜日志弹窗
const showErrorDialog = ref(false); // 钥匙柜编号选择错误弹窗
const addKey = ref(false); // 新增钥匙柜弹窗第一个
const addKeyTwo = ref(false); // 新增钥匙柜弹窗第二个
const active = ref(0); // 初始化步骤状态默认第一步
const bindVehicleDialog = ref(false); // 绑定车辆弹窗第二步骤
const loading = ref(false); // 树加载
const filterLicense = ref(""); // 车牌号搜索(树)
const fullyOpenDialog = ref(false); // 一键开门弹窗
const editKeyCabinet = ref(false); //编辑弹窗
const clearDialog = ref(false); //清空弹窗
const countdown = ref(0); // 时间
const canClear = ref(false); // 清空弹窗控制
const unbindDialog = ref(false); // 解绑弹窗
let timer: NodeJS.Timeout | null = null; // 时间
const parkList = ref([]); //场站列表
const corpTree = ref([]);
const corpTreeProp = {
  value: "IdStr",
  label: "Name",
  children: "children"
};
const selectCorpIds = ref([]);
const queryCropTree = ref(null); //查询的机构树
const addCorpTreeRef = ref(null); //新增的机构树
const isEffectiveKeyCode = ref(false); //判断钥匙柜编号是否有效
const addKeyDetail = ref({}); //钥匙柜添加时的详情
const addTableRef = ref(null); //添加弹窗的表格
const addTableData = ref([]);
const bindTreeRef = ref(null);
const checkTreeNodeNum = ref(0);
const vehicleTreeData = ref([]); //车辆数数据
const isMoreBind = ref(false); //是否为批量绑定
const nowTableIndex = ref(0);
const nowTableKey = ref("");
const defaultCheckedKeys = ref([]);
const bindVehicleId = ref(undefined);
const bindTreeKey = ref(null);
const nowKeyCabinetData = ref({});
const openLoading = ref(false);
const editFormData = ref({}); //编辑表单
const editFormRef = ref(null);
const editTableData = ref([]);
const editTableRef = ref(null);
const operateType = ref("add");
const allKeyNum = ref(0);
const checkTableData = ref([]);
const editLookTitle = ref("编辑");

//------------------查询表单----------------------
const PresenceList = [
  { value: 1, label: "在线" },
  { value: 2, label: "离线" }
]; //在线状态列表
const activeStateList = [
  { value: 1, label: "正常" },
  { value: 2, label: "异常" }
]; //使用状态列表

//柜格数验证规则
const boxNumRules = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请输入整数"));
  } else if (!Number.isInteger(value)) {
    callback(new Error("请输入整数"));
  } else if (value < 1 || value > 60) {
    callback(new Error("请输入1到60之间的整数"));
  } else {
    callback();
  }
};

//form表单验证规则
const formRules = ref<FormRules>({
  Name: [
    { required: true, message: "请输入钥匙柜名称", trigger: ["blur", "change"] }
  ],
  Code: [
    { required: true, message: "请输入钥匙柜编号", trigger: ["blur", "change"] }
  ],
  ParkingId: [
    { required: true, message: "请选择关联场站", trigger: ["blur", "change"] }
  ],
  BoxNum: [
    {
      required: true,
      validator: boxNumRules,
      trigger: ["blur", "change"]
    }
  ]
});

//表格数据
const tableList = ref<keyCabinetListItems[]>([]);
//查询条件类型
interface paramsTypeOne {
  CorporationIds?: [number]; //机构id
  ParkingIds?: [number]; //场站ID
  Code?: string; //钥匙柜编号
  License?: string; //车牌号
  BoxNum?: number; //钥匙柜格数
  IsOnline?: number; //在线状态
  UseStatus?: number; //使用状态
  NotUseBoxMin?: number; //最小空闲
  NotUseBoxMax?: number; //最大空闲
  Offset?: number; // 从第几行开始查询
  Limit?: number; // 查询数据的个数
  CorporationIdStr?: [string];
}

//查询表单分页参数
const pageParamOne = reactive({
  total: 0,
  offset: 0,
  limit: 10
});

//查询条件
const queryForm = reactive({});

// 记录每次查询操作的请求参数
let queryReqOne = reactive<paramsTypeOne>({});

//分页切换类型
interface pageTypeOne {
  page: number;
  limit: number;
}

// 分页切换
const handleChangePageOne = (row: pageTypeOne) => {
  const { limit, page } = JSON.parse(JSON.stringify(row));
  pageParamOne.offset = limit * (page - 1);
  pageParamOne.limit = limit;
  getTableData(queryReqOne);
};

//查询
const handleQuery = () => {
  pageParamOne.total = 0;
  pageParamOne.offset = 0;
  const req = cloneDeep(queryForm);
  queryReqOne = req;
  queryReqOne.CorporationIds = queryReqOne.CorporationIdStr?.map(item => {
    return JSONbig.parse(item);
  });

  getTableData(queryReqOne);
};
//查询请求
const getTableData = async (req: paramsTypeOne) => {
  const res = await getKeyCabinetList({
    ...req,
    IsOnline: req.IsOnline || null,
    UseStatus: req.UseStatus || null,
    Offset: pageParamOne.offset,
    Limit: pageParamOne.limit
  });
  let data = res?.Data?.Items ?? [];
  data = changeTableData(data);
  tableList.value = data;
  pageParamOne.total = res?.Data?.TotalCount ?? 0;
};

//返回数据处理
const changeTableData = (data: keyCabinetListItems[]) => {
  data.forEach((d: keyCabinetListItems, i: number) => {
    d.Seq = pageParamOne.offset + i + 1;
    d.IsOnlineStr =
      d.IsOnline === 1 ? "在线" : d.IsOnline === 2 ? "离线" : "未知";
    d.UseStatusStr =
      d.UseStatus === 1 ? "正常" : d.UseStatus === 2 ? "异常" : "未知";
  });
  return data;
};

// 新增钥匙柜表单
const formModel = reactive({});
//----------------------钥匙柜日志-----------------------
//钥匙柜日志查询条件

const queryLogForm = ref<any>({
  timeRange: [
    timeFormat(new Date().getTime(), "YYYY-MM-DD"),
    timeFormat(new Date().getTime(), "YYYY-MM-DD")
  ]
}); //钥匙柜日志查询条件
//钥匙柜日志弹窗
const keycabinetLogs = () => {
  keyCabinetLog.value = true;
  handleSearch();
};
//搜索日志
const handleSearch = () => {
  pageParamTwo.total = 0;
  pageParamTwo.offset = 0;

  queryReqTwo = cloneDeep(queryLogForm.value);
  getTableDatas();
};
//搜索请求
const getTableDatas = () => {
  openLogCabinet({
    ...queryReqTwo,
    StartAt: queryReqTwo.timeRange?.length
      ? queryReqTwo.timeRange[0] + " 00:00:00"
      : null,
    EndAt: queryReqTwo.timeRange?.length
      ? queryReqTwo.timeRange[1] + " 23:59:59"
      : null,
    Offset: pageParamTwo.offset,
    Limit: pageParamTwo.limit
  }).then(res => {
    const data = res?.Data?.Items ?? [];
    data?.forEach(item => {
      item.WorkPostTypeStr = workPostTypeToStr(item.WorkPostType);
      item.FromAgentStr =
        item.FromAgent === "web"
          ? "web"
          : item.FromAgent === "wxos"
          ? "小程序"
          : item.FromAgent === "dingding"
          ? "钉钉"
          : "未知";
      item.BoxDetailStr =
        item.IsOpenAll === 1 ? "全部打开" : "打开" + item.BoxDetail;
    });
    logTableData.value = data;
    pageParamTwo.total = res?.Data?.TotalCount ?? 0;
  });
};
//导出日志
const handleExport = () => {
  const req = cloneDeep(queryLogForm.value);
  openLogCabinet({
    ...req,
    StartAt: req.timeRange?.length ? req.timeRange[0] + " 00:00:00" : null,
    EndAt: req.timeRange?.length ? req.timeRange[1] + " 23:59:59" : null,
    Offset: 0,
    Limit: 99999999
  }).then(res => {
    let data = res?.Data?.Items;
    let exportData = [
      ["操作人", "岗位", "操作时间", "钥匙柜编号", "所属场站", "操作", "控制端"]
    ];
    data?.forEach(item => {
      item.WorkPostTypeStr = workPostTypeToStr(item.WorkPostType);
      item.FromAgentStr =
        item.FromAgent === "web"
          ? "web"
          : item.FromAgent === "wxos"
          ? "小程序"
          : item.FromAgent === "dingding"
          ? "钉钉"
          : "未知";
      item.BoxDetailStr =
        item.IsOpenAll === 1 ? "全部打开" : "打开" + item.BoxDetail;
      exportData.push([
        item.OpUserName,
        item.WorkPostTypeStr,
        item.CreatedAt,
        item.Code,
        item.ParkingName,
        item.BoxDetailStr,
        item.FromAgentStr
      ]);
    });
    exportXlsx({
      data: exportData,
      jtsOpts: {
        skipHeader: true
      },
      sheetName: "钥匙柜日志",
      fileName: `钥匙柜日志.xlsx`
    });
  });
};

const logTableData = ref([]); //钥匙柜表格数据

// 日志分页参数
const pageParamTwo = reactive({
  total: 0,
  offset: 0,
  limit: 10,
  order: "desc"
});

let queryReqTwo = reactive({});
//日志分页切换类型
interface pageTypeTwo {
  page: number;
  limit: number;
}
// 日志分页切换操作
const handleChangePageTwo = (row: pageTypeTwo) => {
  const { limit, page } = JSON.parse(JSON.stringify(row)); // 获取分页参数
  pageParamTwo.offset = limit * (page - 1);
  pageParamTwo.limit = limit;
  getTableDatas();
};
//日志弹窗关闭
const handleLogClose = () => {
  keyCabinetLog.value = false;
  queryLogForm.value = {
    timeRange: [
      timeFormat(new Date().getTime(), "YYYY-MM-DD"),
      timeFormat(new Date().getTime(), "YYYY-MM-DD")
    ]
  };
};

//---------------------新增钥匙柜第一步------------------
const keyCabinetFormRef = ref<FormInstance>(); // 新增钥匙柜表单实例引用

const rowData = [
  { licensePlate: "浙J12345D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" },
  { licensePlate: "浙J67890D" }
]; // 车辆表格数据

//新增钥匙柜弹窗
const addKeyCabinet = () => {
  allKeyNum.value = 0;
  keyCabinetFormRef.value?.resetFields();
  checkTableData.value = [];
  addTableData.value = [];
  active.value = 0;
  addKey.value = true;
  operateType.value = "add";
};

//新增钥匙柜编号验证
const validateCabinetCode = () => {
  if (!formModel.Code) {
    return;
  }
  checkStatusKeyCabinet({ Code: formModel.Code }).then(res => {
    // isEffectiveKeyCode
    const data = res?.Data;
    addKeyDetail.value = data;

    if (data?.IsExist) {
      ElMessage({
        message: "钥匙柜编号已存在,请重新填写",
        type: "error"
      });
      return;
    }
    if (!data?.IsOnline) {
      ElMessage({
        message: "钥匙柜不在线,请重新填写",
        type: "error"
      });
      return;
    }
    isEffectiveKeyCode.value = !data?.IsExist && data?.IsOnline;
    if (formModel.check) {
      formModel.BoxNum = data.BoxNum;
    }
    allKeyNum.value = formModel.BoxNum;
  });
};

//新增钥匙柜时自动获取变化
const checkChange = val => {
  if (val) {
    formRules.value.BoxNum = [
      {
        required: false
      }
    ];
    formModel.BoxNum = addKeyDetail.value.BoxNum;
    allKeyNum.value = addKeyDetail.value.BoxNum;
  } else {
    allKeyNum.value = formModel.BoxNum;
    formRules.value.BoxNum = [
      {
        required: true,
        validator: boxNumRules,
        trigger: ["blur", "change"]
      }
    ];
  }
};

//下一步弹窗
const nextStep = () => {
  if (!isEffectiveKeyCode.value) {
    ElMessage({
      message: "钥匙柜编号错误,请重新填写",
      type: "error"
    });
    // return;
  }
  keyCabinetFormRef.value?.validate((valid: boolean, err) => {
    if (valid) {
      active.value = 1;
      addKey.value = false;
      addKeyTwo.value = true;
      allKeyNum.value = formModel.BoxNum;

      if (!addTableData?.value?.length) {
        addTableData.value = formattedTableDataOne(formModel.BoxNum);
      }
    }
  });
};

//下一步取消
const nextCancel = () => {
  addKey.value = false;
  active.value = 0;
};
//-------------钥匙柜选择错误-----------------------
//钥匙柜编号选择错误弹窗
const closeErrorDialog = () => {
  showErrorDialog.value = false;
};

//绑定车辆弹窗
const bindVehicle = (isMore, data, key, index) => {
  isMoreBind.value = isMore;
  bindVehicleDialog.value = true;
  nowTableIndex.value = index;
  nowTableKey.value = key;
  bindTreeKey.value = new Date().getTime();
  if (isMore) {
    checkTreeNodeNum.value = 0;
    if (operateType.value === "edit") {
      checkTreeNodeNum.value = checkTableData.value?.length;
      const checkIds = checkTableData.value?.map(item => {
        for (const x in item) {
          if (x.includes("VehicleId_")) {
            return item[x];
          }
        }
      });
      setTimeout(() => {
        defaultCheckedKeys.value = checkIds;
      }, 1000);
    } else {
      defaultCheckedKeys.value = [];
    }
  } else {
    const value = data["VehicleId_" + key.split("_")[1]];
    setTimeout(() => {
      bindVehicleId.value = value;
    }, 1000);
  }
};
//清空绑定的车辆
const clearBindVehicle = () => {
  addTableData.value = formattedTableDataOne(formModel.BoxNum);
};

//新增钥匙柜生成列表
const formattedTableDataOne = num => {
  const numRow = 15; // 表格总行
  const numCols = 4; // 表格总列
  const dataWithSequence = [];
  for (let rowIndex = 0; rowIndex < numRow; rowIndex++) {
    const row = {}; // 创建当前行
    // 两列一组，遍历每一组
    for (let colIndex = 0; colIndex < numCols; colIndex++) {
      // 计算当前序号
      const sequenceNumber = rowIndex * numCols + colIndex + 1;

      if (num && num < sequenceNumber) {
        break;
      }
      // 序号和车牌号添加到当前行
      row[`BoxNo_${colIndex + 1}`] = sequenceNumber;
    }
    if (JSON.stringify(row) === "{}") {
      break;
    }
    // 当前行添加到数组
    dataWithSequence.push(row);
  }
  return dataWithSequence;
};
// 表格列配置(新增钥匙柜)
const columnsOne = [
  { prop: "BoxNo_1", label: "序号", minWidth: 60 },
  { prop: "License_1", label: "车牌", minWidth: 120 },
  { prop: "BoxNo_2", label: "序号", minWidth: 60 },
  { prop: "License_2", label: "车牌", minWidth: 120 },
  { prop: "BoxNo_3", label: "序号", minWidth: 60 },
  { prop: "License_3", label: "车牌", minWidth: 120 },
  { prop: "BoxNo_4", label: "序号", minWidth: 60 },
  { prop: "License_4", label: "车牌", minWidth: 120 }
];

//上一步
const previousStep = () => {
  active.value = 0;
  addKey.value = true;
  addKeyTwo.value = false;
  console.log("formModel", formModel);
};

//完成
const submitForm = () => {
  const data = [];
  addTableData.value.forEach(item => {
    for (let i = 1; i <= 4; i++) {
      const boxNoKey = `BoxNo_${i}`;
      const licenseKey = `License_${i}`;
      const vehicleIdKey = `VehicleId_${i}`;

      if (item[boxNoKey]) {
        data.push({
          BoxNo: item[boxNoKey],
          License: item[licenseKey],
          VehicleId: item[vehicleIdKey]
        });
      }
    }
  });
  let req = {
    ...formModel,
    CorporationId: JSONbig.parse(formModel.CorporationId || 0),
    Boxes: data
  };
  createKeyCabinet(req).then(res => {
    ElMessage({
      message: "新增成功",
      type: "success"
    });
    addKeyTwo.value = false;
    handleQuery();
  });
};

//第二步骤取消
const nextCancelTwo = () => {
  addKeyTwo.value = false;
  Object.assign(formModel, {
    cabinetCode: null,
    location: "",
    locationId: "",
    cabinetNumber: null,
    check: false
  });
  active.value = 0;
};

//-------------------第二步骤绑定车辆---------------------
//绑定车辆关闭
const addBindVehicleCancel = () => {
  bindVehicleDialog.value = false;
};
//------------------树-------------------
// 组件传参
const props = defineProps({
  data: Array, // 树初始数据
  width: String, // 树宽度
  type: Number, // 树类型, 参考枚举：TreeType
  showSearch: { type: Boolean, default: true },
  showCheck: { type: Boolean, default: false } // 是否显示复选框
});

// 树节点复选框选中变化
const bingTreeCheckChange = () => {
  if (!isMoreBind.value) {
    return;
  }
  checkTreeNodeNum.value = bindTreeRef.value
    .getCheckedNodes()
    ?.filter(v => v.type == "vehicle")?.length;
};
//单个节点绑定
const bindSingleVehicle = () => {
  if (isMoreBind.value) {
    return;
  }
};

//绑定车辆确定
const defineBindTree = () => {
  if (isMoreBind.value) {
    const data = bindTreeRef.value
      .getCheckedNodes()
      ?.filter(v => v.type == "vehicle");
    if (operateType.value === "add") {
      addTableData.value = formattedTableDataOne(formModel.BoxNum);
      addTableData.value?.forEach(ele => {
        data?.forEach((item, index) => {
          if (ele.BoxNo_1 === index + 1) {
            ele.License_1 = item.Name;
            ele.VehicleId_1 = item.Id;
          } else if (ele.BoxNo_2 === index + 1) {
            ele.License_2 = item.Name;
            ele.VehicleId_2 = item.Id;
          } else if (ele.BoxNo_3 === index + 1) {
            ele.License_3 = item.Name;
            ele.VehicleId_3 = item.Id;
          } else if (ele.BoxNo_4 === index + 1) {
            ele.License_4 = item.Name;
            ele.VehicleId_4 = item.Id;
          }
        });
      });
      bindTreeRef.value.setCheckedKeys([]);
    } else {
      if (checkTableData.value?.length) {
        checkTableData.value?.forEach((item, index) => {
          data?.forEach((ele, ind) => {
            if (ind === index) {
              for (const y in item) {
                if (y.includes("License_")) {
                  item[y] = ele.Name;
                }
                if (y.includes("VehicleId_")) {
                  item[y] = ele.Id;
                }
              }
            }
          });
        });
        checkTableData.value?.forEach(item => {
          for (const y in item) {
            for (const x in editTableData.value[item.dataIndex]) {
              if (
                x === y &&
                (x.includes("License_") || x.includes("VehicleId_"))
              ) {
                editTableData.value[item.dataIndex][x] = item[y];
              }
            }
          }
        });
      } else {
        editTableData.value = formattedTableDataOne(allKeyNum.value);
        editTableData.value?.forEach(ele => {
          data?.forEach((item, index) => {
            if (ele.BoxNo_1 === index + 1) {
              ele.License_1 = item.Name;
              ele.VehicleId_1 = item.Id;
            } else if (ele.BoxNo_2 === index + 1) {
              ele.License_2 = item.Name;
              ele.VehicleId_2 = item.Id;
            } else if (ele.BoxNo_3 === index + 1) {
              ele.License_3 = item.Name;
              ele.VehicleId_3 = item.Id;
            } else if (ele.BoxNo_4 === index + 1) {
              ele.License_4 = item.Name;
              ele.VehicleId_4 = item.Id;
            }
          });
        });
      }
    }
  } else {
    const index = nowTableIndex.value;
    const keyParts = nowTableKey.value.split("_")[1];
    const data = bindTreeRef.value.getCurrentNode();

    if (data && data.type == "vehicle") {
      if (operateType.value === "add") {
        for (let i = 0; i < addTableData.value.length; i++) {
          const item = addTableData.value[i];
          for (const key in item) {
            if (key.includes("VehicleId") && item[key] === data.Id) {
              ElMessage({
                message: "当前车辆已绑定",
                type: "warning"
              });
              return;
            }
          }
        }
        addTableData.value[index]["License_" + keyParts] = data.Name;
        addTableData.value[index]["VehicleId" + keyParts] = data.Id;
      } else {
        for (let i = 0; i < editTableData.value.length; i++) {
          const item = editTableData.value[i];
          for (const key in item) {
            if (key.includes("VehicleId") && item[key] === data.Id) {
              ElMessage({
                message: "当前车辆已绑定",
                type: "warning"
              });
              return;
            }
          }
        }
        editTableData.value[index]["License_" + keyParts] = data.Name;
        editTableData.value[index]["VehicleId" + keyParts] = data.Id;
      }
    } else {
      if (operateType.value === "add") {
        addTableData.value[index]["License_" + keyParts] = null;
        addTableData.value[index]["VehicleId" + keyParts] = null;
      } else {
        editTableData.value[index]["License_" + keyParts] = null;
        editTableData.value[index]["VehicleId" + keyParts] = null;
      }
    }
  }
  bindVehicleCancel();
};

//绑定车辆取消
const bindVehicleCancel = () => {
  bindVehicleDialog.value = false;
};
//----------------------操作-----------------------
//-------------------------一键全开--------------------
//一键全开弹窗
const handleFullyOpen = row => {
  nowKeyCabinetData.value = row;
  fullyOpenDialog.value = true;
};

//一键全开取消
const cancelFull = () => {
  fullyOpenDialog.value = false;
};

//一键全开确定
const defineFull = () => {
  openLoading.value = true;
  openBoxKeyCabinet({
    IsAll: 1,
    Id: nowKeyCabinetData.value?.Id,
    From: "web"
  }).then(res => {
    ElMessage({
      message: "柜门开启指令已下发,请确认",
      type: "success"
    });
    openLoading.value = false;
    cancelFull();
  });
  // nowKeyCabinetData
};

//开单个柜门
const openBoxDoor = (row, prop) => {
  ElMessageBox.confirm("是否开启当前柜门?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      openBoxKeyCabinet({
        IsAll: 2,
        Id: JSONbig.parse(editFormData.value?.Id),
        License: row[prop],
        From: "web"
      }).then(res => {
        ElMessage({
          message: "柜门开启指令已下发,请确认",
          type: "success"
        });
      });
    })
    .catch(() => {});
};

//--------------------编辑----------------
//操作编辑弹窗
const handleEdit = (row, type) => {
  ShowKeyCabinetDetail({ Id: row.Id, Code: row.Code }).then(res => {
    editFormData.value = JSON.parse(JSON.stringify(res?.Data));
    const Boxes = res?.Data?.Boxes;
    if (res?.Data?.CorporationId === 0) {
      editFormData.value.CorporationId = "";
    }
    editLookTitle.value = type === "look" ? "查看" : "编辑";
    editKeyCabinet.value = true;
    operateType.value = type;
    allKeyNum.value = res?.Data?.BoxNum;
    editTableData.value = formattedTableDataOne(res?.Data?.BoxNum);
    editTableData.value?.forEach(ele => {
      Boxes?.forEach(item => {
        if (ele.BoxNo_1 === item.BoxNo) {
          ele.License_1 = item.License;
          ele.VehicleId_1 = item.VehicleId;
        } else if (ele.BoxNo_2 === item.BoxNo) {
          ele.License_2 = item.License;
          ele.VehicleId_2 = item.VehicleId;
        } else if (ele.BoxNo_3 === item.BoxNo) {
          ele.License_3 = item.License;
          ele.VehicleId_3 = item.VehicleId;
        } else if (ele.BoxNo_4 === item.BoxNo) {
          ele.License_4 = item.License;
          ele.VehicleId_4 = item.VehicleId;
        }
      });
    });
  });
};

// 表格列配置(编辑)
const columnsTwo = [
  { prop: "selected_1", label: "", minWidth: 40 }, // 勾选框列
  { prop: "BoxNo_1", label: "序号", minWidth: 60 },
  { prop: "License_1", label: "车牌", minWidth: 120 },
  { prop: "selected_2", label: "", minWidth: 40 }, // 勾选框列
  { prop: "BoxNo_2", label: "序号", minWidth: 60 },
  { prop: "License_2", label: "车牌", minWidth: 120 },
  { prop: "selected_3", label: "", minWidth: 40 }, // 勾选框列
  { prop: "BoxNo_3", label: "序号", minWidth: 60 },
  { prop: "License_3", label: "车牌", minWidth: 120 },
  { prop: "selected_4", label: "", minWidth: 40 }, // 勾选框列
  { prop: "BoxNo_4", label: "序号", minWidth: 60 },
  { prop: "License_4", label: "车牌", minWidth: 120 }
];

//操作编辑生成列表
const formattedTableDataTwo = computed(() => {
  const numRow = 15; // 表格总行
  const numCols = 4; // 每组序号和车牌信息（包括勾选框）
  const dataWithSequence = [];

  for (let rowIndex = 0; rowIndex < numRow; rowIndex++) {
    const row = {}; // 创建当前行
    for (let colIndex = 0; colIndex < numCols; colIndex++) {
      // 每一组序号和车牌信息
      const sequenceNumber = rowIndex * numCols + colIndex + 1;
      const licensePlate =
        rowData[rowIndex * numCols + colIndex]?.licensePlate || ""; // 获取对应车牌信息

      // 添加勾选框默认未选中
      row[`selected${colIndex + 1}`] = false; // 默认不选中
      row[`BoxNo_${colIndex + 1}`] = sequenceNumber;
      row[`plate${colIndex + 1}`] = licensePlate;
    }
    dataWithSequence.push(row);
  }
  return dataWithSequence;
});

// 表头复选框的状态
const headerCheckStates = ref(Array(columnsTwo.length).fill(false));

// 处理表头复选框变更事件
function handleHeaderCheckboxChange(index: number) {
  const isChecked = headerCheckStates.value[index]; // 获取当前表头的状态
  const colIndex = Math.floor(index / 3); // 三列为一组
  editTableData.value.forEach(row => {
    row[`selected_${colIndex + 1}`] = isChecked;
  });
  getCheckTableData();
}

// 处理行内复选框变更事件
function handleCheckboxChange(row, prop: string) {
  const colIndex = columnsTwo.findIndex(col => col.prop === prop); //查找当前列索引
  const selectedCount = editTableData.value.filter(
    item => item[`selected_${Math.ceil((colIndex + 1) / 3)}`]
  ).length; // 获取选中的行数

  const totalCount = editTableData.value.length; // 获取总行数
  headerCheckStates.value[colIndex] = selectedCount === totalCount;
  getCheckTableData();
}

//获取勾选项
const getCheckTableData = () => {
  checkTableData.value = [];
  for (let i = 0; i < editTableData.value?.length; i++) {
    const item = editTableData.value[i];
    for (const key in item) {
      if (key?.includes("selected") && item[key]) {
        const keyNum = key.split("_")[1];
        const BoxNoKey = "BoxNo_" + keyNum;
        const LicenseKey = "License_" + keyNum;
        const VehicleKey = "VehicleId_" + keyNum;
        checkTableData.value.push({
          dataIndex: i,
          [BoxNoKey]: item[BoxNoKey],
          [LicenseKey]: item[LicenseKey],
          [VehicleKey]: item[VehicleKey]
        });
      }
    }
  }
};

//删除操作
const handleDel = row => {
  console.log("row", row);

  if (row.IsExistVehicle) {
    ElMessageBox.confirm("该钥匙柜已绑定车辆,无法删除", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "error"
    })
      .then(() => {})
      .catch(() => {});
  } else {
    deleteKeyCabinet({ Id: row.Id }).then(res => {
      ElMessage({
        message: "删除成功",
        type: "success"
      });
      handleQuery();
    });
  }
};

//清空绑定
const handleClearEdit = () => {
  clearDialog.value = true;
  if (timer) clearInterval(timer);
  countdown.value = 5;
  canClear.value = false;
  timer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      clearInterval(timer!);
      canClear.value = true;
    }
  }, 1000);
};

//清空绑定取消
const cancelClear = () => {
  clearDialog.value = false;
  resetCountdown();
};

//清空绑定清空
const defineClear = () => {
  if (canClear.value) {
    editTableData.value = formattedTableDataOne(allKeyNum.value);
    startDownClear();
  }
};

//清空确认
const startDownClear = () => {
  clearDialog.value = false;
  resetCountdown();
};

//清空倒计时
const resetCountdown = () => {
  countdown.value = 0;
  canClear.value = false;
  if (timer !== null) {
    clearInterval(timer);
    timer = null;
  }
};

//编辑取消
const handleCancelEdit = () => {
  editKeyCabinet.value = false;
  editFormRef.value?.resetFields();
  editTableData.value = [];
  headerCheckStates.value.fill(false);
  checkTreeNodeNum.value = 0;
  allKeyNum.value = 0;
};

//编辑确定
const handleDefineEdit = () => {
  const data = [];
  editTableData.value.forEach(item => {
    for (let i = 1; i <= 4; i++) {
      const boxNoKey = `BoxNo_${i}`;
      const licenseKey = `License_${i}`;
      const vehicleIdKey = `VehicleId_${i}`;

      if (item[boxNoKey]) {
        data.push({
          BoxNo: item[boxNoKey],
          License: item[licenseKey],
          VehicleId: item[vehicleIdKey]
        });
      }
    }
  });
  const req = {
    ...editFormData.value,
    Id: JSONbig.parse(editFormData.value.Id),
    CorporationId: JSONbig.parse(editFormData.value.CorporationId || 0),
    TopCorporationId: JSONbig.parse(editFormData.value.TopCorporationId || 0)
  };
  updateKeyCabinet({ ...req, Boxes: data }).then(res => {
    ElMessage({
      message: "编辑成功",
      type: "success"
    });
    handleCancelEdit();
    handleQuery();
  });
};
//--------------------批量解绑---------------------
//批量解绑
const handleUnbindEdit = () => {
  if (!checkTableData.value?.length) {
    ElMessage({
      message: "请选择车辆",
      type: "warning"
    });
    return;
  }
  unbindDialog.value = true;
  if (timer) clearInterval(timer);
  countdown.value = 3;
  canClear.value = false;
  timer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      clearInterval(timer!);
      canClear.value = true;
    }
  }, 1000);
};

//批量解绑取消
const cancelUnbind = () => {
  unbindDialog.value = false;
  resetCountdown();
};

//批量解绑确定
const defineUnbind = () => {
  if (canClear.value) {
    checkTableData.value?.forEach(item => {
      for (const y in item) {
        for (const x in editTableData.value[item.dataIndex]) {
          if (x === y && (x.includes("License_") || x.includes("VehicleId_"))) {
            editTableData.value[item.dataIndex][x] = null;
          }
        }
      }
    });
    startDownUnbind();
  }
};

//解绑确认
const startDownUnbind = () => {
  unbindDialog.value = false;
  resetCountdown();
};

//获取场站列表
getParkingList({ Offset: 0, Limit: 9999999, Order: "desc" }).then(res => {
  parkList.value =
    res?.Data?.Items?.filter(
      (item, index, self) => index === self.findIndex(t => t.Id === item.Id)
    ) || [];
});

//获取机构列表
// getTreeList({
//   Offset: 0,
//   Limit: 9999999,
//   Order: "desc",
//   TreeType: 1
// }).then(res => {
//   corpTree.value = buildCorpTree(res?.Data?.Items);
//   selectCorpIds.value = corpTree?.value?.map(item => item.IdStr);
// });

//获取车辆树
// getTreeList({
//   Offset: 0,
//   Limit: 9999999,
//   Order: "desc",
//   TreeType: 3
// }).then(res => {
//   const data = JSON.parse(JSON.stringify(res?.Data?.Items));
//   vehicleTreeData.value = buildVehicleTree(data);
// });

//车辆数筛选
const filterLicenseNode = (value: string, data) => {
  if (!value) return true;
  return data.Name.includes(value);
};

// 导入-弹框是否显示
const importDialog_show = ref(false);
// 导入-文件名
const importDialog_fileName = ref("");
// 导入-文件对象
let importDialog_file = null;

// 导入-点击导入
const handleImport = row => {
  nowKeyCabinetData.value = row;
  importDialog_show.value = true;
};

// 导入-取消
const importDialog_cancel = () => {
  importDialog_fileName.value = false;
  nowKeyCabinetData.value = {};
  importDialog_file = null;
};
// 导入-确认
const handleImport_confirm = () => {
  if (!importDialog_file) {
    ElMessage.warning("请上传文件");
    return;
  } else {
    const reader = new FileReader();
    reader.readAsDataURL(importDialog_file);
    reader.onload = () => {
      const fileData = reader.result;
      ImportLicense({
        Id: nowKeyCabinetData.value.Id,
        FileData: fileData.split(";base64,")[1]
      }).then(res => {
        const data = res?.Data;
        const msg =
          "导入" +
          data.TotalCount +
          "条数,成功" +
          data.SuccessCount +
          "条,失败" +
          data.FailCount +
          "条";
        ElMessage.info(msg);
        importDialog_cancel();
      });
    };
  }
};

// 导入-下载模板
const handleImport_download = () => {
  const a = document.createElement("a");
  a.href = `/static/钥匙柜导入.xlsx`;
  a.download = `钥匙柜导入.xlsx`;
  a.style.display = "none";
  document.body.appendChild(a);
  a.click();
  a.remove();
};
// 导入-上传文件
const importDialog_upload = ({ file }) => {
  if (
    file.type !==
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ) {
    ElMessage.error("文件格式有误");
    return;
  }
  importDialog_fileName.value = file.name;
  importDialog_file = file;
};

watch(filterLicense, val => {
  bindTreeRef.value!.filter(val);
});
//------------------------------------

//监听
watch(
  () => props.type,
  (newVal, oldVal) => {
    if (oldVal) {
      loading.value = true;
      getTreeData(newVal).then(data => {
        treeData.value = data || [];
        loading.value = false;
      });
    }
  }
);
</script>
<style lang="scss">
#deviceLogs {
  .el-checkbox {
    display: flex;
    justify-content: flex-end;
    .el-checkbox__label {
      margin-right: 145px;
    }
  }
  .select-form {
    width: 180px;
    margin-right: 20px;
  }
  .select-formTwo {
    // display: flex;
    // flex-wrap: wrap;
    width: 180px;
  }
  .select-formThree {
    width: 140px;
    margin-right: 20px;
  }
  .select-formFour {
    width: 240px;
    margin-right: 20px;
  }
  .form-group {
    display: flex;
    padding: 10px 10px 0px 0px;
  }

  .bind-vehicle {
    display: flex;
    justify-content: flex-end;
  }
  .addEditDialog {
    .el-table {
      height: 60vh;
    }
    .steps-one {
      padding: 20px 20px 20px 0px;
    }
    .steps-two {
      padding: 20px 20px 0px 0px;
    }

    .tree-container {
      height: 50vh;
      width: 100%;
      display: flex;
      flex-direction: column;
      background: #fff;
      .treeBox {
        flex: 1;
        overflow: auto;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-top: 20px;
      }
    }
    .log-del {
      margin-left: 480px;
      margin-top: -40px;
      width: 120px;
      height: 35px;
    }
    .edit-body {
      display: flex;
      .edit-body-one {
        width: 80%;
        margin-left: 50px;
      }
    }
    .edit-body-botton {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
    .publicDialog {
      .publicDialog-one {
        display: flex;
      }
      .publicDialog-two {
        margin-left: 10px;
        font-weight: bolder;
        font-size: 16px;
      }
      .publicDialog-three {
        display: flex;
        justify-content: flex-start;
        margin-top: 30px;
        .publicDialog-four {
          width: 120px;
          margin-left: 50px;
        }
        .publicDialog-five {
          width: 120px;
        }
        .publicDialog-six {
          width: 120px;
          margin-left: 120px;
        }
      }
    }
    .add-KeyCabinetForm {
      display: flex;
      flex-wrap: wrap;
      margin-left: 50px;
    }
    .add-KeyCabinetFormTwo {
      display: flex;
      justify-content: flex-start;
      padding: 0px 0px 0px 50px;
    }
  }

  .next-class {
    display: flex;
    justify-content: flex-end;
    padding: 20px 25px 10px 0px;
  }
  .el-popper {
    max-width: 400px;
  }
  .uploadBox {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px;
    .elUpload {
      width: 100%;
      .el-upload {
        width: 100%;
        .el-upload-dragger {
          width: 100%;
        }
      }
      .el-upload__tip {
        color: red;
      }
    }
    .btns {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
